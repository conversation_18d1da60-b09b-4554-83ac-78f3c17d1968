# DataModel Generator

The DataModel Generator is a command-line tool that automatically generates C# model classes from XML schema files. It creates strongly-typed data models for SharePoint lists and libraries used in the Atvero Email Filing system.

## Overview

This tool reads XML schema definitions from either local files or downloads them from `https://schema.atvero.com`, then generates corresponding C# classes that inherit from `BaseItem`. The generated models provide type-safe access to SharePoint list fields and include methods for CRUD operations.

## Features

- **Automatic Schema Download**: Downloads the latest XML schema files from the Atvero schema repository
- **Type-Safe Model Generation**: Creates strongly-typed C# classes with proper field mappings
- **Multiple Field Types**: Supports Text, Number, Boolean, DateTime, Lookup, and Multi-Lookup fields
- **CRUD Operations**: Generated models include Create, Read, Update, and Delete methods
- **Configurable Output**: Allows custom output directory specification
- **Pre-build Integration**: Can be integrated into MSBuild process for automatic regeneration

## Command Line Usage

### Basic Usage

```bash
# Generate models using downloaded schemas (default behavior)
dotnet run -- generate

# Generate models from local schema directory
dotnet run -- generate --schema /path/to/schemas

# Generate models to custom output directory
dotnet run -- generate --output /path/to/output

# Generate only a specific model class
dotnet run -- generate --class Project

# Enable verbose output for debugging
dotnet run -- generate --verbose
```

### Command Line Options

| Option | Short | Description | Required | Default |
|--------|-------|-------------|----------|---------|
| `--schema` | `-s` | Path to directory containing XML schema files | No | Downloads from schema.atvero.com |
| `--output` | `-o` | Output directory for generated model files | No | `../CMapPim/GeneratedModels` |
| `--class` | `-c` | Generate only a specific class (optional) | No | All classes |
| `--verbose` | `-v` | Enable verbose output for debugging | No | `false` |

### Examples

```bash
# Download latest schemas and generate all models
dotnet run -- generate

# Use local schemas and output to custom directory
dotnet run -- generate --schema ./schemas --output ./models

# Generate only the Project model with verbose output
dotnet run -- generate --class Project --verbose

# Generate models for CMapPim project (typical usage)
dotnet run -- generate --output ../CMapPim/GeneratedModels
```

## Integration with MSBuild

The DataModel generator is integrated into the CMapPim project's build process via a pre-build target:

```xml
<Target Name="RunDataModelGenerator" BeforeTargets="BeforeBuild">
  <Message Text="Running DataModel generator..." Importance="high" />
  <Exec Command="dotnet run --project $(MSBuildProjectDirectory)/../DataModel/DataModel.csproj -- generate --output $(MSBuildProjectDirectory)/GeneratedModels"
        ContinueOnError="false"
        WorkingDirectory="$(MSBuildProjectDirectory)" />
</Target>
```

This ensures that:
- Models are always up-to-date with the latest schema
- Build fails if model generation fails
- No manual intervention required
- Works in both local development and CI/CD environments

## Generated Model Structure

Each generated model class includes:

### Base Structure
- Inherits from `CMapPim.Model.BaseItem`
- Contains static field definitions for different data types
- Implements type-safe property accessors

### Field Types
- **String Fields**: Text and Note fields from SharePoint
- **Integer Fields**: Number fields
- **Boolean Fields**: Yes/No fields
- **DateTime Fields**: Date and time fields
- **Lookup Fields**: Single-value lookups to other lists
- **Multi-Lookup Fields**: Multi-value lookups to other lists

### Generated Methods
- **Constructors**: Multiple constructors for different initialization scenarios
- **CRUD Operations**: Create, Read, Update, Delete methods
- **Field Accessors**: Type-safe getters and setters for all fields
- **Lookup Resolution**: Methods to resolve lookup field values

### Example Generated Class

```csharp
public class Project : Model.BaseItem
{
    private static string table = "Projects";

    // Field definitions
    private static string[] stringAttributes = new string[]
    {
        "ATVImportedSourceID",
        "Title",
        "ProjectName",
        "Description"
    };

    // Property accessors
    public string Title
    {
        get { return GetFieldAsString("Title"); }
        set { SetFieldAsString("Title", value); }
    }

    // CRUD methods
    public static async Task<Result<Project>> Create(Context ctx, Project project)
    public static async Task<Result<List<Project>>> Find(Context ctx, string filter, string expand)
    public async Task<Result> Update(Context ctx)
    public async Task<Result> Delete(Context ctx)
}
```

## Schema File Format

The generator expects XML schema files with the following structure:

```xml
<List Name="Projects" Type="100">
  <Fields>
    <Field Name="Title" Type="Text" Required="TRUE" />
    <Field Name="ProjectName" Type="Text" />
    <Field Name="StartDate" Type="DateTime" />
    <Field Name="IsActive" Type="Boolean" />
    <Field Name="CompanyLookup" Type="Lookup" List="Company" />
    <Field Name="ContactsMulti" Type="LookupMulti" List="Contact" />
  </Fields>
</List>
```

### Supported Field Types
- `Text`: String properties
- `Note`: String properties (multi-line text)
- `Number`: Integer properties
- `Boolean`: Boolean properties
- `DateTime`: DateTime? properties
- `Lookup`: Single lookup to another list
- `LookupMulti`: Multi-value lookup to another list

## Supported SharePoint Lists

The generator supports the following SharePoint lists/libraries:

### Hub Site Lists
- Company, CompanyAddress, ProjectRoleCategories, ProjectRoles
- Contact, Projects, ContactsLists, Document Groups, Document Types
- HubsiteParts, Integrations, ISO19650Roles, MetaBlocks, NamingSchemes
- ProjectContacts, ProjectOwners, ProjectSettings, Quick Parts
- RecordLists, RequiredFields, Schemas, Sidebars, TenantSettings

### Project Site Lists
- AutoNumbers, DLM_Revisions, DLM_Placeholders, DynamicParts
- Issues, Issued_Contacts, Issued_Revisions, Received, Received_Revisions
- RecordLists, Saved_Filters

### Document Libraries
- DLM_Library, Confidential_DLM_Library
- DMS_Library, Confidential_DMS_Library

## Error Handling

The generator includes comprehensive error handling:

- **Schema Download Failures**: Graceful handling of network issues
- **Invalid XML**: Clear error messages for malformed schema files
- **Missing Dependencies**: Warnings for unresolved lookup references
- **File System Errors**: Proper error reporting for output directory issues

## Troubleshooting

### Common Issues

1. **Build Failures**: Check that the DataModel project compiles successfully
2. **Network Issues**: Verify internet connectivity for schema downloads
3. **Permission Errors**: Ensure write permissions to output directory
4. **Missing Schemas**: Check that all required XML files are available

### Verbose Mode

Use the `--verbose` flag to get detailed information about:
- Schema file processing
- Field type mapping
- Code generation steps
- Error details and stack traces

### Manual Execution

If the pre-build integration fails, you can run the generator manually:

```bash
cd DataModel
dotnet run -- generate --output ../CMapPim/GeneratedModels --verbose
```

## Development

### Adding New Field Types

To add support for new SharePoint field types:

1. Update the field type detection logic in `GenerateAtveroModel`
2. Add appropriate property generation code
3. Update the field arrays (stringAttributes, intAttributes, etc.)
4. Test with sample schema files

### Adding New Lists

To add support for new SharePoint lists:

1. Add entry to `classTables` dictionary
2. Add to appropriate list order (`hubListsInOrder` or `siteListsInOrder`)
3. Create corresponding XML schema file
4. Test model generation

### Modifying Generated Code

The generated code templates are embedded in the `GenerateAtveroModel` method. To modify:

1. Locate the relevant code generation section
2. Update the string concatenation logic
3. Test with sample schemas
4. Verify generated code compiles and functions correctly

## Architecture

### Project Structure

```
DataModel/
├── DataModel.cs          # Main entry point and command-line interface
├── ModelGenerator.cs     # Core model generation logic
├── DataModel.csproj      # Project file with dependencies
└── README.md            # This documentation
```

### Dependencies

- **CommandLineParser**: For parsing command-line arguments
- **FluentResults**: For error handling and result types
- **System.Xml.Linq**: For XML schema parsing

### Code Generation Process

1. **Schema Loading**: Load XML schema files from local directory or download from remote
2. **Field Analysis**: Parse XML to identify field types, names, and relationships
3. **Code Generation**: Generate C# class code using string templates
4. **File Output**: Write generated classes to specified output directory

### Generated Code Dependencies

Generated models depend on:
- `CMapPim.Model.BaseItem`: Base class providing common functionality
- `FluentResults`: For method return types
- `Microsoft.Graph.Models`: For SharePoint integration
- `AtveroEmailFiling.Services.GraphApiService`: For API operations

## Performance Considerations

- **Incremental Generation**: Only regenerates when schemas change
- **Parallel Processing**: Could be enhanced for concurrent file processing
- **Memory Usage**: Efficient string building for large schemas
- **Build Time**: Minimal impact on overall build time

## Security Considerations

- **Schema Validation**: Validates XML structure before processing
- **Output Sanitization**: Ensures generated code is safe
- **Network Security**: Uses HTTPS for schema downloads
- **File Permissions**: Respects file system permissions

## Future Enhancements

### Planned Features
- **Incremental Updates**: Only regenerate changed models
- **Custom Templates**: Support for custom code generation templates
- **Validation Rules**: Generate field validation based on schema constraints
- **Documentation Generation**: Auto-generate API documentation
- **Unit Test Generation**: Generate unit tests for models

### Performance Improvements
- **Caching**: Cache downloaded schemas locally
- **Parallel Processing**: Process multiple schemas concurrently
- **Optimized Output**: Use StringBuilder for better performance
- **Smart Regeneration**: Only regenerate when schemas actually change

## Contributing

When contributing to the DataModel generator:

1. **Follow Coding Standards**: Use consistent naming and formatting
2. **Add Tests**: Include unit tests for new functionality
3. **Update Documentation**: Keep README.md current with changes
4. **Validate Output**: Ensure generated code compiles and functions correctly
5. **Performance Testing**: Verify changes don't impact build performance

## License

This tool is part of the Atvero Email Filing system and follows the same licensing terms.
