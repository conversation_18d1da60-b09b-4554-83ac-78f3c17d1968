# DataModel Generator - Quick Reference

## Command Line Cheat Sheet

```bash
# Basic usage - download schemas and generate all models
dotnet run -- generate

# Use local schemas
dotnet run -- generate --schema /path/to/schemas

# Custom output directory
dotnet run -- generate --output /path/to/output

# Generate specific class only
dotnet run -- generate --class Project

# Verbose output for debugging
dotnet run -- generate --verbose

# Combined options
dotnet run -- generate --schema ./schemas --output ./models --verbose
```

## Common Use Cases

### Development Workflow
```bash
# 1. Make schema changes
# 2. Regenerate models
cd DataModel
dotnet run -- generate --output ../CMapPim/GeneratedModels

# 3. Build and test
cd ../CMapPim
dotnet build
```

### Troubleshooting
```bash
# Debug generation issues
dotnet run -- generate --verbose

# Test specific model
dotnet run -- generate --class Project --verbose

# Use local schemas to avoid network issues
dotnet run -- generate --schema ./local-schemas --verbose
```

### CI/CD Integration
```bash
# In build pipeline
dotnet run --project DataModel/DataModel.csproj -- generate --output CMapPim/GeneratedModels
```

## File Locations

| Path | Description |
|------|-------------|
| `DataModel/DataModel.cs` | Main entry point |
| `DataModel/ModelGenerator.cs` | Core generation logic |
| `CMapPim/GeneratedModels/` | Default output directory |
| `https://schema.atvero.com/66/` | Remote schema repository |

## Generated Model Features

### Every Model Includes
- Inherits from `BaseItem`
- Type-safe property accessors
- CRUD operations (Create, Read, Update, Delete)
- Field validation
- Lookup resolution

### Property Types
- `string` - Text/Note fields
- `int` - Number fields  
- `bool` - Boolean fields
- `DateTime?` - DateTime fields
- `List<int>` - Multi-lookup fields
- `int` - Single lookup fields

### Common Methods
```csharp
// Create new item
var result = await Project.Create(context, projectInstance);

// Find items
var projects = await Project.Find(context, "Fields/Title eq 'Test'", "Fields");

// Update item
await project.Update(context);

// Delete item
await project.Delete(context);
```

## Supported SharePoint Lists

### Hub Site Lists (25 lists)
- Company, CompanyAddress, Contact, Projects
- ProjectRoles, ProjectContacts, ProjectOwners
- Document Groups, Document Types, Schemas
- TenantSettings, Sidebars, Quick Parts
- And more...

### Project Site Lists (19 lists)  
- AutoNumbers, DLM_Revisions, DLM_Placeholders
- Issues, Issued_Contacts, Issued_Revisions
- Received, Received_Revisions, Saved_Filters
- And more...

### Document Libraries (4 libraries)
- DLM_Library, Confidential_DLM_Library
- DMS_Library, Confidential_DMS_Library

## Field Type Mapping

| SharePoint Type | C# Type | Generated Property |
|-----------------|---------|-------------------|
| Text | string | `public string FieldName { get; set; }` |
| Note | string | `public string FieldName { get; set; }` |
| Number | int | `public int FieldName { get; set; }` |
| Boolean | bool | `public bool FieldName { get; set; }` |
| DateTime | DateTime? | `public DateTime? FieldName { get; set; }` |
| Lookup | int | `public int FieldNameLookupId { get; set; }` |
| LookupMulti | List<int> | `public string FieldNameAsString { get; set; }` |

## Error Messages

| Error | Cause | Solution |
|-------|-------|----------|
| "Failed to download source file" | Network/URL issue | Check internet connection or use local schemas |
| "Warning, can't find list X" | Missing lookup target | Add missing list to classTables dictionary |
| "Unable to find file for path" | Missing schema file | Ensure all required XML files exist |
| Build errors in generated code | Invalid schema | Validate XML schema structure |

## MSBuild Integration

The generator runs automatically during CMapPim builds via:

```xml
<Target Name="RunDataModelGenerator" BeforeTargets="BeforeBuild">
  <Message Text="Running DataModel generator..." Importance="high" />
  <Exec Command="dotnet run --project $(MSBuildProjectDirectory)/../DataModel/DataModel.csproj -- generate --output $(MSBuildProjectDirectory)/GeneratedModels" 
        ContinueOnError="false" 
        WorkingDirectory="$(MSBuildProjectDirectory)" />
</Target>
```

## Performance Tips

- Use `--class` to generate only needed models during development
- Local schemas (`--schema`) are faster than downloads
- Generated models are cached until schemas change
- Pre-build integration adds ~2-5 seconds to build time

## Debugging Tips

1. **Use verbose mode**: `--verbose` shows detailed processing info
2. **Check schema files**: Validate XML structure manually
3. **Test individual models**: Use `--class` to isolate issues
4. **Verify output**: Check generated .cs files for syntax errors
5. **Build incrementally**: Generate one model at a time when debugging

## Quick Fixes

### "Schema download failed"
```bash
# Use local schemas instead
dotnet run -- generate --schema ./backup-schemas
```

### "Generated code won't compile"
```bash
# Check for XML validation issues
dotnet run -- generate --class ProblemClass --verbose
```

### "Build is slow"
```bash
# Skip regeneration if schemas haven't changed
# (Manual check - automatic detection planned)
```

### "Missing lookup references"
```bash
# Check classTables dictionary in ModelGenerator.cs
# Add missing list mappings
```

## Schema File Structure

Minimal required XML structure:
```xml
<List Name="ListName" Type="100">
  <Fields>
    <Field Name="Title" Type="Text" Required="TRUE" />
    <Field Name="CustomField" Type="Text" />
  </Fields>
</List>
```

Required attributes:
- `List.Name`: SharePoint list name
- `List.Type`: List type (100=custom, 101=document library)
- `Field.Name`: Internal field name
- `Field.Type`: SharePoint field type

Optional attributes:
- `Field.Required`: TRUE/FALSE
- `Field.List`: Target list for lookups
- `Field.Description`: Field description
- `Field.DisplayName`: Display name
