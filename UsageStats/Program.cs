﻿using System.Globalization;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using CsvHelper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

public class Person
{
    public string? Name { get; set; }
    public int FilingCount { get; set; }
}

public class Project
{
    public string? Name { get; set; }
    public int FilingCount { get; set; }
}

public class Program
{
    public static async Task Main()
    {
        // Console.WriteLine("Generating usage stats");

        var configuration = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("local.settings.json", optional: true, reloadOnChange: true)
            .AddEnvironmentVariables()
            .Build();

        string? tableStorageUri = configuration["Values:TableStorageUri"];
        string? accountName = configuration["Values:StorageAccountName"];
        string? accountKey = configuration["Values:StorageAccountKey"];

        List<string> accounts = new List<string>()
        {
            "acgarchitectscouk",
            "aitkenturnbullcouk",
            "ajaltdcouk",
            "arcdesignstudiocouk",
            "architypecouk",
            "arrowplanningcouk",
            "baynhammeiklecouk",
            "boothkingcouk",
            "buttressnet",
            "c2cconsultingcouk",
            "calibroconsultantscom",
            "clarmancom",
            "cluttonscoms",
            "cmapio",
            "criteriumengineerscom",
            "cwconcouk",
            "designbrookcouk",
            "dmfkcouk",
            "elliottwoodcouk",
            "harrisandsloancom",
            "harrisbuggcom",
            "hdccouk",
            "heatherwickcom",
            "hlmarchitectscom",
            "hlpukcom",
            "mcadamirelandcom",
            "mcadamdesigncouk",
            "millieuconsultcom",
            "oobecouk",
            "pentancouk",
            "pjscecouk",
            "purcellaucom",
            "purcellukcom",
            "rckacouk",
            "senateconsultingcouk",
            "silverdcccom",
            "sraarcchitectscouk",
            "studiolbacouk",
            "wilkinsoneyrecom",
            "willishazellcom",
            "milieuconsultcom",
            "ajaltdcouk",
            "studiolbacouk",
            "hdccouk",
        };

        int total_filed = 0;

        if (tableStorageUri != null && accountName != null && accountKey != null)
        {
            using ILoggerFactory factory = LoggerFactory.Create(builder => builder.AddConsole());
            ILogger<AzureTableServiceClient> logger =
                factory.CreateLogger<AzureTableServiceClient>();

            IAzureTableService tableService = new AzureTableServiceClient(
                tableStorageUri,
                accountName,
                accountKey,
                logger
            );

            foreach (string customer in accounts)
            {
                try
                {
                    //   Console.WriteLine("Getting stats on customer " + customer);

                    List<FiledEmail> filedEmails =
                        await tableService.QueryEntitiesAsync<FiledEmail>(
                            "FiledEmails",
                            //$"PartitionKey eq '{filedEmail.RowKey}' and RowKey eq '{unfileEmailRequest.MessageId}'",
                            "",
                            customer
                        );

                    // Console.WriteLine("Found " + filedEmails.Count + "  filed emails");

                    Dictionary<string, int> users = new Dictionary<string, int>();
                    Dictionary<string, int> projects = new Dictionary<string, int>();

                    foreach (FiledEmail email in filedEmails)
                    {
                        string lowerUser = email.FiledBy.ToLower();

                        if (users.ContainsKey(lowerUser))
                        {
                            int count = users[lowerUser];
                            users[lowerUser] = count + 1;
                        }
                        else
                        {
                            users[lowerUser] = 1;
                        }

                        string lowerProject = email.ProjectCode.ToLower();

                        if (projects.ContainsKey(lowerProject))
                        {
                            int count = projects[lowerProject];
                            projects[lowerProject] = count + 1;
                        }
                        else
                        {
                            projects[lowerProject] = 1;
                        }
                    }

                    //  Console.WriteLine(projects.Keys.Count + " projects filed to");

                    var total = 0;
                    foreach (string project in projects.Keys)
                    {
                        int count = projects[project];
                        //Console.WriteLine(project + "," + count);
                        total = total + count;
                    }

                    total_filed = total_filed + total;

                    // Console.WriteLine(users.Keys.Count + " users have filed");
                    // foreach (string user in users.Keys)
                    // {
                    //     int count = users[user];
                    //     Console.WriteLine(user + "," + count);
                    // }

                    Console.WriteLine(
                        customer + "," + users.Keys.Count + "," + projects.Keys.Count + "," + total
                    );

                    string filePath = customer + "_people.csv";

                    // Write to CSV file
                    using (var writer = new StreamWriter(filePath))
                    using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                    {
                        csv.WriteRecords(users);
                    }

                    string projectFilePath = customer + "_projects.csv";

                    // Write to CSV file
                    using (var writer = new StreamWriter(projectFilePath))
                    using (var csv = new CsvWriter(writer, CultureInfo.InvariantCulture))
                    {
                        csv.WriteRecords(projects);
                    }
                }
                catch (Exception ex)
                {
                    // Console.WriteLine("Failed to process " + customer + " " + ex.Message);
                }

                Console.WriteLine("Total filed: " + total_filed);
            }
        }
        else
        {
            Console.WriteLine("Configuration is missing");
        }
    }
}
