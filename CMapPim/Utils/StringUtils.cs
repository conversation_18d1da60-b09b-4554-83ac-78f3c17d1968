using FluentResults;

namespace CMapPim.Utils
{
    public static class StringUtils
    {
        public static string? FirstNameFromFullname(string? fullname)
        {
            char[] whitespace = new char[] { ' ', '\t', '\r', '\n' };
            return fullname
                ?.Split(whitespace, StringSplitOptions.RemoveEmptyEntries)
                .FirstOrDefault();
        }

        public static string? LastNameFromFullname(string? fullname)
        {
            char[] whitespace = new char[] { ' ', '\t', '\r', '\n' };
            return fullname
                ?.Split(whitespace, StringSplitOptions.RemoveEmptyEntries)
                .LastOrDefault();
        }

        public static Result<string> SitePathToSite(string sitePath)
        {
            if (string.IsNullOrEmpty(sitePath))
            {
                return Result.Fail("Site path is empty");
            }
            if (2 == sitePath.Split("/").Length)
            {
                return Result.Ok(sitePath.Split("/").Last());
            }
            else
            {
                return Result.Fail("Cannot extract site from site path " + sitePath);
            }
        }

        public static string? Take(string? s, int toTake)
        {
            if (string.IsNullOrEmpty(s))
            {
                return s;
            }
            if (s.Length < toTake)
            {
                return s;
            }

            return s.Substring(0, toTake);
        }

        public static string? FilterInvalidXmlChars(string? v)
        {
            if (string.IsNullOrEmpty(v))
            {
                return v;
            }
            char[] validXmlChars = v.Where(ch => System.Xml.XmlConvert.IsXmlChar(ch)).ToArray();
            return new string(validXmlChars);
        }
    }
}
