using System;

namespace CMapPim.Utils
{
    public static class ResultHelpers
    {
        public static FluentResults.Result FailWithException(string message, Exception e)
        {
            return FluentResults.Result.Fail(new FluentResults.Error(message).CausedBy(e));
        }

        public static FluentResults.Result FailWithReason(string message, string reason)
        {
            return FluentResults.Result.Fail(new FluentResults.Error(message).CausedBy(reason));
        }
    }
}
