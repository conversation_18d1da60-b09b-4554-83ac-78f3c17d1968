using CMapPim.Model;
using FluentResults;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Kiota.Abstractions.Serialization;
using Newtonsoft.Json;

namespace CMapPim.Utils
{
    public class GraphUtils
    {
        public static List<string>? getLookupIdsFromMultiLookupField(UntypedArray multiLookupResult)
        {
            List<string>? returnList = null;
            List<Int32> intList = GetLookupIdsFromMultiLookupFieldAsInt(multiLookupResult);
            if (intList != null)
            {
                returnList = intList.Select(i => i.ToString()).ToList();
            }
            return null;
        }

        public static List<Int32> GetLookupIdsFromMultiLookupFieldAsInt(
            UntypedArray multiLookupResult
        )
        {
            List<Int32> result = new List<Int32>();

            if (multiLookupResult?.GetValue() != null)
            {
                foreach (var item in multiLookupResult.GetValue())
                {
                    if (item is UntypedObject untypedObj && untypedObj.GetValue() != null)
                    {
                        // Look for LookupId or LookupValue in the object
                        if (untypedObj.GetValue().TryGetValue("LookupId", out var lookupIdValue))
                        {
                            if (
                                lookupIdValue is UntypedString stringValue
                                && stringValue.GetValue() != null
                            )
                            {
                                if (Int32.TryParse(stringValue.GetValue(), out Int32 id))
                                {
                                    result.Add(id);
                                }
                            }
                            else if (lookupIdValue is UntypedInteger intValue)
                            {
                                result.Add(intValue.GetValue());
                            }
                        }
                        else if (
                            untypedObj.GetValue().TryGetValue("LookupValue", out var lookupValue)
                        )
                        {
                            if (
                                lookupValue is UntypedString stringValue
                                && stringValue.GetValue() != null
                            )
                            {
                                if (Int32.TryParse(stringValue.GetValue(), out Int32 id))
                                {
                                    result.Add(id);
                                }
                            }
                            else if (lookupValue is UntypedInteger intValue)
                            {
                                result.Add(intValue.GetValue());
                            }
                        }
                    }
                    else if (item is UntypedString directString && directString.GetValue() != null)
                    {
                        // Handle case where the array contains direct string values
                        if (Int32.TryParse(directString.GetValue(), out Int32 id))
                        {
                            result.Add(id);
                        }
                    }
                    else if (item is UntypedInteger directInt)
                    {
                        // Handle case where the array contains direct integer values
                        result.Add(directInt.GetValue());
                    }
                }
            }

            return result;
        }

        public static Stream CopyStream(Stream inputStream)
        {
            MemoryStream ms = new MemoryStream();

            inputStream.Seek(0, 0);
            inputStream.CopyTo(ms);
            inputStream.Seek(0, 0);

            ms.Seek(0, 0);

            return ms;
        }

        public static string CleanGuid(string guid)
        {
            string cleanedGuid = Guid.Parse(guid as string).ToString();
            return cleanedGuid;
        }

        public static Result<string> GetSharePointSubdomain(string webUrl)
        {
            Uri webUri = new Uri(webUrl);
            string[] parts = webUri.Host.Split(".");
            if (0 == parts.Length)
            {
                return Result.Fail("Can't split domain for sptenant " + webUrl);
            }

            return Result.Ok(parts[0]);
        }
    }
}
