using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.GeneratedModels
{
  public class DynamicPart : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
  //  private string _sourcePath =  "";
    private static string table = "DynamicParts";
    private  static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVDelimiter",
      "ATVValue",
      "ATVGuid",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVPosition",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
      "ATVParentValueLookupId",
    };
    
    private new static string[] boolAttributeNames = new string[]
    {
      "ATVDefaultValue",
      "ATVDLMDefaultValue",
      "ATVDMSDefaultValue",
    };
    
    private new static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVDelimiter
      {
        get { return GetFieldAsString("ATVDelimiter");}
        set { SetFieldAsString("ATVDelimiter",value);}
      }
    
    public string ATVValue
      {
        get { return GetFieldAsString("ATVValue");}
        set { SetFieldAsString("ATVValue",value);}
      }
    
    public string ATVGuid
      {
        get { return GetFieldAsString("ATVGuid");}
        set { SetFieldAsString("ATVGuid",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVPosition
      {
        get { return GetFieldAsInteger("ATVPosition");}
        set { SetFieldAsInteger("ATVPosition",value);}
      }
    
    public int ATVParentValue
      {
        get { return GetFieldAsSingleLookup("ATVParentValueLookupId");}
        set { SetFieldAsSingleLookup("ATVParentValueLookupId",value);}
      }
    
    public bool ATVDefaultValue
      {
        get { return GetFieldAsBool("ATVDefaultValue");}
        set { SetFieldAsBool("ATVDefaultValue",value);}
      }
    
    public bool ATVDLMDefaultValue
      {
        get { return GetFieldAsBool("ATVDLMDefaultValue");}
        set { SetFieldAsBool("ATVDLMDefaultValue",value);}
      }
    
    public bool ATVDMSDefaultValue
      {
        get { return GetFieldAsBool("ATVDMSDefaultValue");}
        set { SetFieldAsBool("ATVDMSDefaultValue",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private DynamicPart(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public DynamicPart(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public DynamicPart()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<DynamicPart>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,

                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new DynamicPart(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<DynamicPart>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<DynamicPart> mappedItems = new List<DynamicPart>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new DynamicPart(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<DynamicPart>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess && null != limRes.Value)
      {
        return new DynamicPart(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the DynamicPart");
        return Result.Fail("There was an error creating the DynamicPart");
      }
    }

    public async Task<Result<ListItem?>> Update(Context ctx)
    {
    ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public async Task<Result> Delete(Context ctx)
    {
          string? id = GetId().ToString();
      if (id == null)
      {
        return Result.Fail("No id set");
      }
      else
        {
        Result limRes = await ctx.GraphApiClient.DeleteListItem(id, table, ctx.SiteId, ctx.GraphClient);
        return limRes;
      }
    }

    /*
    public static Result Write(List<DynamicPart> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVDelimiter","ATVValue","ATVGuid","Title","ATVPosition","ATVParentValue","ATVDefaultValue","ATVDLMDefaultValue","ATVDMSDefaultValue");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<DynamicPart> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<DynamicPart>().ToList();
    }
*/
       }
}
