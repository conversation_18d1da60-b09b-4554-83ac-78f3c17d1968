using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.GeneratedModels
{
  public class IssuedContact : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
  //  private string _sourcePath =  "";
    private static string table = "Issued_Contacts";
    private  static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVIssuedContactGuid",
      "ATVIssueMedia",
      "ATVTransmittalRef",
      "ATVSharingLink",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVIssueCopies",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
      "ATVIssueLookupId",
    };
    
    private new static string[] boolAttributeNames = new string[]
    {
    };
    
    private new static string[] dateTimeAttributeNames = new string[]
    {
      "ATVTokenExpiry",
      "ATVTransmittedOn",
    };
  
    public string ATVIssuedContactGuid
      {
        get { return GetFieldAsString("ATVIssuedContactGuid");}
        set { SetFieldAsString("ATVIssuedContactGuid",value);}
      }
    
    public string ATVIssueMedia
      {
        get { return GetFieldAsString("ATVIssueMedia");}
        set { SetFieldAsString("ATVIssueMedia",value);}
      }
    
    public string ATVTransmittalRef
      {
        get { return GetFieldAsString("ATVTransmittalRef");}
        set { SetFieldAsString("ATVTransmittalRef",value);}
      }
    
    public string ATVSharingLink
      {
        get { return GetFieldAsString("ATVSharingLink");}
        set { SetFieldAsString("ATVSharingLink",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVIssueCopies
      {
        get { return GetFieldAsInteger("ATVIssueCopies");}
        set { SetFieldAsInteger("ATVIssueCopies",value);}
      }
    
    public int ATVIssue
      {
        get { return GetFieldAsSingleLookup("ATVIssueLookupId");}
        set { SetFieldAsSingleLookup("ATVIssueLookupId",value);}
      }
    
    public DateTime? ATVTokenExpiry
      {
        get { return GetFieldAsDateTime("ATVTokenExpiry");}
        set { SetFieldAsDateTime("ATVTokenExpiry",value);}
      }
    
    public DateTime? ATVTransmittedOn
      {
        get { return GetFieldAsDateTime("ATVTransmittedOn");}
        set { SetFieldAsDateTime("ATVTransmittedOn",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private IssuedContact(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public IssuedContact(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public IssuedContact()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<IssuedContact>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,

                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new IssuedContact(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<IssuedContact>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<IssuedContact> mappedItems = new List<IssuedContact>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new IssuedContact(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<IssuedContact>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess && null != limRes.Value)
      {
        return new IssuedContact(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the IssuedContact");
        return Result.Fail("There was an error creating the IssuedContact");
      }
    }

    public async Task<Result<ListItem?>> Update(Context ctx)
    {
    ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public async Task<Result> Delete(Context ctx)
    {
          string? id = GetId().ToString();
      if (id == null)
      {
        return Result.Fail("No id set");
      }
      else
        {
        Result limRes = await ctx.GraphApiClient.DeleteListItem(id, table, ctx.SiteId, ctx.GraphClient);
        return limRes;
      }
    }

    /*
    public static Result Write(List<IssuedContact> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVIssuedContactGuid","ATVIssueMedia","ATVTransmittalRef","ATVSharingLink","Title","ATVIssueCopies","ATVIssue","ATVTokenExpiry","ATVTransmittedOn");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<IssuedContact> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<IssuedContact>().ToList();
    }
*/
       }
}
