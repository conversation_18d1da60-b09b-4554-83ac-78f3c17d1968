using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.GeneratedModels
{
  public class SavedFilter : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
  //  private string _sourcePath =  "";
    private static string table = "Saved_Filters";
    private  static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVFilters",
      "ATVColumns",
      "ATVColumnSortKey",
      "ATVGroupBy",
      "ATVPage",
      "ATVReceivedString",
      "ATVSuitabilityFilter",
      "ATVDocGroupFilter",
      "ATVCompanyGuidFilter",
      "ATVGuid",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
      "ATVSliderState",
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
    };
    
    private new static string[] boolAttributeNames = new string[]
    {
    };
    
    private new static string[] dateTimeAttributeNames = new string[]
    {
      "ATVFilterStartDate",
      "ATVFilterEndDate",
    };
  
    public string ATVFilters
      {
        get { return GetFieldAsString("ATVFilters");}
        set { SetFieldAsString("ATVFilters",value);}
      }
    
    public string ATVColumns
      {
        get { return GetFieldAsString("ATVColumns");}
        set { SetFieldAsString("ATVColumns",value);}
      }
    
    public string ATVColumnSortKey
      {
        get { return GetFieldAsString("ATVColumnSortKey");}
        set { SetFieldAsString("ATVColumnSortKey",value);}
      }
    
    public string ATVGroupBy
      {
        get { return GetFieldAsString("ATVGroupBy");}
        set { SetFieldAsString("ATVGroupBy",value);}
      }
    
    public string ATVPage
      {
        get { return GetFieldAsString("ATVPage");}
        set { SetFieldAsString("ATVPage",value);}
      }
    
    public string ATVReceivedString
      {
        get { return GetFieldAsString("ATVReceivedString");}
        set { SetFieldAsString("ATVReceivedString",value);}
      }
    
    public string ATVSuitabilityFilter
      {
        get { return GetFieldAsString("ATVSuitabilityFilter");}
        set { SetFieldAsString("ATVSuitabilityFilter",value);}
      }
    
    public string ATVDocGroupFilter
      {
        get { return GetFieldAsString("ATVDocGroupFilter");}
        set { SetFieldAsString("ATVDocGroupFilter",value);}
      }
    
    public string ATVCompanyGuidFilter
      {
        get { return GetFieldAsString("ATVCompanyGuidFilter");}
        set { SetFieldAsString("ATVCompanyGuidFilter",value);}
      }
    
    public string ATVGuid
      {
        get { return GetFieldAsString("ATVGuid");}
        set { SetFieldAsString("ATVGuid",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVSliderState
      {
        get { return GetFieldAsInteger("ATVSliderState");}
        set { SetFieldAsInteger("ATVSliderState",value);}
      }
    
    public DateTime? ATVFilterStartDate
      {
        get { return GetFieldAsDateTime("ATVFilterStartDate");}
        set { SetFieldAsDateTime("ATVFilterStartDate",value);}
      }
    
    public DateTime? ATVFilterEndDate
      {
        get { return GetFieldAsDateTime("ATVFilterEndDate");}
        set { SetFieldAsDateTime("ATVFilterEndDate",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private SavedFilter(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public SavedFilter(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public SavedFilter()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<SavedFilter>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,

                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new SavedFilter(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<SavedFilter>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<SavedFilter> mappedItems = new List<SavedFilter>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new SavedFilter(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<SavedFilter>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess && null != limRes.Value)
      {
        return new SavedFilter(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the SavedFilter");
        return Result.Fail("There was an error creating the SavedFilter");
      }
    }

    public async Task<Result<ListItem?>> Update(Context ctx)
    {
    ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public async Task<Result> Delete(Context ctx)
    {
          string? id = GetId().ToString();
      if (id == null)
      {
        return Result.Fail("No id set");
      }
      else
        {
        Result limRes = await ctx.GraphApiClient.DeleteListItem(id, table, ctx.SiteId, ctx.GraphClient);
        return limRes;
      }
    }

    /*
    public static Result Write(List<SavedFilter> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVFilters","ATVColumns","ATVColumnSortKey","ATVGroupBy","ATVPage","ATVReceivedString","ATVSuitabilityFilter","ATVDocGroupFilter","ATVCompanyGuidFilter","ATVGuid","Title","ATVSliderState","ATVFilterStartDate","ATVFilterEndDate");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<SavedFilter> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<SavedFilter>().ToList();
    }
*/
       }
}
