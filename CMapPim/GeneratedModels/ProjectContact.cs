using FluentResults;
using Microsoft.Extensions.Logging;
using Sylvan.Data;
using Sylvan.Data.Csv;
using AtveroEmailFiling.Services.GraphApiService;
using CMapPim.Model;
using Microsoft.Graph.Models;
namespace CMapPim.GeneratedModels
{
  public class ProjectContact : Model.BaseItem
   {
    private string sourceID =  "0";
    public static bool exporting = true;
  //  private string _sourcePath =  "";
    private static string table = "ProjectContacts";
    private  static string[] stringAttributes = new string[]
    {
      "ATVImportedSourceID",
      "ATVContactGuid",
      "ATVProjectUser",
      "Title",
    };
    
    private static string[] intAttributes = new string[]
    {
    };
    
    private static string[] multiLookupAttributes = new string[]
    {
    };
    
    private static string[] singleLookupAttributes = new string[]
    {
      "ATVProjectLookupId",
      "ATVContactLookupId",
      "ATVCompanyAddressLookupId",
      "ATVCompanyLookupId",
      "ATVISO19650RoleLookupId",
      "ATVProjectRoleLookupId",
      "ATVProjectRoleCategoryLookupId",
    };
    
    private new static string[] boolAttributeNames = new string[]
    {
      "ATVContactIsImportant",
      "ATVIsHiddenFlag",
    };
    
    private new static string[] dateTimeAttributeNames = new string[]
    {
    };
  
    public string ATVContactGuid
      {
        get { return GetFieldAsString("ATVContactGuid");}
        set { SetFieldAsString("ATVContactGuid",value);}
      }
    
    public string ATVProjectUser
      {
        get { return GetFieldAsString("ATVProjectUser");}
        set { SetFieldAsString("ATVProjectUser",value);}
      }
    
    public string Title
      {
        get { return GetFieldAsString("Title");}
        set { SetFieldAsString("Title",value);}
      }
    
    public int ATVProject
      {
        get { return GetFieldAsSingleLookup("ATVProjectLookupId");}
        set { SetFieldAsSingleLookup("ATVProjectLookupId",value);}
      }
    
    public int ATVContact
      {
        get { return GetFieldAsSingleLookup("ATVContactLookupId");}
        set { SetFieldAsSingleLookup("ATVContactLookupId",value);}
      }
    
    public int ATVCompanyAddress
      {
        get { return GetFieldAsSingleLookup("ATVCompanyAddressLookupId");}
        set { SetFieldAsSingleLookup("ATVCompanyAddressLookupId",value);}
      }
    
    public int ATVCompany
      {
        get { return GetFieldAsSingleLookup("ATVCompanyLookupId");}
        set { SetFieldAsSingleLookup("ATVCompanyLookupId",value);}
      }
    
    public int ATVISO19650Role
      {
        get { return GetFieldAsSingleLookup("ATVISO19650RoleLookupId");}
        set { SetFieldAsSingleLookup("ATVISO19650RoleLookupId",value);}
      }
    
    public int ATVProjectRole
      {
        get { return GetFieldAsSingleLookup("ATVProjectRoleLookupId");}
        set { SetFieldAsSingleLookup("ATVProjectRoleLookupId",value);}
      }
    
    public int ATVProjectRoleCategory
      {
        get { return GetFieldAsSingleLookup("ATVProjectRoleCategoryLookupId");}
        set { SetFieldAsSingleLookup("ATVProjectRoleCategoryLookupId",value);}
      }
    
    public bool ATVContactIsImportant
      {
        get { return GetFieldAsBool("ATVContactIsImportant");}
        set { SetFieldAsBool("ATVContactIsImportant",value);}
      }
    
    public bool ATVIsHiddenFlag
      {
        get { return GetFieldAsBool("ATVIsHiddenFlag");}
        set { SetFieldAsBool("ATVIsHiddenFlag",value);}
      }
    


     // hardcoded for translation


      public string ATVImportedSourceID
      {
        get {  if (exporting) return sourceID; else return GetFieldAsString("ATVImportedSourceID");}
        set { if (exporting) sourceID = value; else SetFieldAsString("ATVImportedSourceID",value);}
      }
    
      private ProjectContact(CMapPim.Model.GraphListItem listItem, ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger,
        listItem
      )
    {
      CopyValues(listItem);
    }

    

    public ProjectContact(ILogger logger)
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        logger
      ) { }

    

    public ProjectContact()
      : base(
        table,
        stringAttributes,
        intAttributes,
        boolAttributeNames,
        dateTimeAttributeNames,
        multiLookupAttributes,
        singleLookupAttributes,
        null
      ) { }

    

    public static async Task<Result<ProjectContact>> GetById(Context ctx, string id)
    {
      Result<ListItem> limRes = await ctx.GraphApiClient.GetById(table,id,

                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        return new ProjectContact(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else{
        return limRes.ToResult();
      }
    }

    public static async Task<Result<List<ProjectContact>>> Find(
      Context ctx,
      string filter
    )
    {
      Result<List<ListItem>> limRes = await ctx.GraphApiClient.Find(table, filter,
                ctx.SiteId,
                ctx.GraphClient);
      if (limRes.IsSuccess)
      {
        List<ProjectContact> mappedItems = new List<ProjectContact>();
        foreach (ListItem item in limRes.Value)
        {
          mappedItems.Add(new ProjectContact(new GraphListItem(item), ctx.Logger));
        }
        return mappedItems;
      }
      else
        return Result.Fail(limRes.Errors);
    }

    public async Task<Result<ProjectContact>> Create(Context ctx)
    {
      ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.CreateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      if (limRes.IsSuccess && null != limRes.Value)
      {
        return new ProjectContact(new GraphListItem(limRes.Value), ctx.Logger);
      }
      else
      {
        //Console.WriteLine(limRes.Errors.First());
        //Console.WriteLine("There was an error creating the ProjectContact");
        return Result.Fail("There was an error creating the ProjectContact");
      }
    }

    public async Task<Result<ListItem?>> Update(Context ctx)
    {
    ListItem item = PrepareListItem();
      Result<ListItem?> limRes = await ctx.GraphApiClient.UpdateListItem(item, table, ctx.SiteId, ctx.GraphClient);
      return limRes;
    }

    

    public async Task<Result> Delete(Context ctx)
    {
          string? id = GetId().ToString();
      if (id == null)
      {
        return Result.Fail("No id set");
      }
      else
        {
        Result limRes = await ctx.GraphApiClient.DeleteListItem(id, table, ctx.SiteId, ctx.GraphClient);
        return limRes;
      }
    }

    /*
    public static Result Write(List<ProjectContact> rows, string filePath)
    {
      var recordReader = rows.AsDataReader();



      // recordReader = recordReader.Select("ATVContactGuid","ATVProjectUser","Title","ATVProject","ATVContact","ATVCompanyAddress","ATVCompany","ATVISO19650Role","ATVProjectRole","ATVProjectRoleCategory","ATVContactIsImportant","ATVIsHiddenFlag");

       using var csvWriter = CsvDataWriter.Create(filePath);
       csvWriter.Write(recordReader);
       return Result.Ok();
     }
*/
     /*
    public static List<ProjectContact> Read(string filePath)
    {
      CsvDataReaderOptions opts = new CsvDataReaderOptions
      {
        HasHeaders = true,
        Schema = CsvSchema.Nullable
      };
      using CsvDataReader csv = CsvDataReader.Create(filePath, opts);
      return csv.GetRecords<ProjectContact>().ToList();
    }
*/
       }
}
