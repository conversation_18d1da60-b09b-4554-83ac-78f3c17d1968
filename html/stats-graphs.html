<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Statistics Graphs - Atvero Email Filing</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .controls-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        select, input[type="text"], input[type="number"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
        }

        .help-text {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .conditional-fields {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }

        .hidden {
            display: none;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            margin-right: 10px;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .btn-secondary {
            background: #6c757d;
        }

        .chart-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .chart-wrapper {
            position: relative;
            height: 400px;
            margin-bottom: 20px;
        }

        .summary-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            margin-bottom: 30px;
        }

        .summary-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .summary-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            border-left: 4px solid #667eea;
        }

        .summary-card h3 {
            font-size: 2em;
            color: #667eea;
            margin-bottom: 5px;
        }

        .summary-card p {
            color: #666;
            font-weight: 500;
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 15px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #fcc;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-box h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        @media (max-width: 768px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .container {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .controls-container, .chart-container, .summary-container {
                padding: 20px;
            }

            .chart-wrapper {
                height: 300px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📈 Statistics Graphs</h1>
            <p>Visualize statistics data from CMap Mail Filing system with interactive charts</p>
        </div>

        <div class="info-box">
            <h4>🔑 API Key Required</h4>
            <p>This function requires authentication. Please enter your API key below. The key should be configured in your Azure Function settings as <code>ExportStatsApiKey</code> or <code>EXPORT_STATS_API_KEY</code>.</p>
        </div>

        <div class="controls-container">
            <form id="statsForm">
                <div class="form-group">
                    <label for="baseUrl">Azure Function Region:</label>
                    <select id="baseUrl" name="baseUrl" required>
                        <option value="">Select region...</option>
                        <option value="https://atvero-fa-emailfiling-prod.azurewebsites.net/api/GetStatsData">UK (Production)</option>
                        <option value="https://atvero-fa-emailfiling-prod-us-002.azurewebsites.net/api/GetStatsData">US (Production)</option>
                    </select>
                    <div class="help-text">Choose the Azure Function region (UK or US)</div>
                </div>

                <div class="form-group">
                    <label for="apiKey">API Key:</label>
                    <input type="password" id="apiKey" name="apiKey" required>
                    <div class="help-text">Your API key for authentication</div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="statsType">Statistics Type:</label>
                        <select id="statsType" name="statsType" required>
                            <option value="">Select statistics type...</option>
                            <option value="UserStats">User Statistics</option>
                            <option value="DomainStats">Domain Statistics</option>
                            <option value="AutoFilingStats">Auto Filing Statistics</option>
                            <option value="UserClickStats">User Click Statistics</option>
                            <option value="ProjectStats">Project Statistics</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="period">Period:</label>
                        <select id="period" name="period" required>
                            <option value="">Select period...</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="aggregation">Aggregation:</label>
                        <select id="aggregation" name="aggregation" required>
                            <option value="domain">By Domain</option>
                            <option value="total">Total Combined</option>
                            <option value="individual">Individual Items</option>
                        </select>
                        <div class="help-text">How to group the data in the chart</div>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="startYear">Start Year:</label>
                        <input type="number" id="startYear" name="startYear" min="2020" max="2030" required>
                        <div class="help-text">4-digit year (e.g., 2024)</div>
                    </div>

                    <div class="form-group">
                        <label for="endYear">End Year (optional):</label>
                        <input type="number" id="endYear" name="endYear" min="2020" max="2030">
                        <div class="help-text">Defaults to start year if not specified</div>
                    </div>
                </div>

                <div id="weeklyFields" class="conditional-fields hidden">
                    <h4>📅 Weekly Range Options</h4>
                    <p class="help-text">Choose either a specific week or a range of weeks (1-53)</p>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="week">Specific Week:</label>
                            <input type="number" id="week" name="week" min="1" max="53">
                            <div class="help-text">Single week number (1-53)</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="startWeek">Start Week:</label>
                            <input type="number" id="startWeek" name="startWeek" min="1" max="53">
                        </div>
                        <div class="form-group">
                            <label for="endWeek">End Week:</label>
                            <input type="number" id="endWeek" name="endWeek" min="1" max="53">
                        </div>
                    </div>
                </div>

                <div id="monthlyFields" class="conditional-fields hidden">
                    <h4>📅 Monthly Range Options</h4>
                    <p class="help-text">Choose either a specific month or a range of months (1-12)</p>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="month">Specific Month:</label>
                            <input type="number" id="month" name="month" min="1" max="12">
                            <div class="help-text">Single month number (1-12)</div>
                        </div>
                    </div>

                    <div class="form-row">
                        <div class="form-group">
                            <label for="startMonth">Start Month:</label>
                            <input type="number" id="startMonth" name="startMonth" min="1" max="12">
                        </div>
                        <div class="form-group">
                            <label for="endMonth">End Month:</label>
                            <input type="number" id="endMonth" name="endMonth" min="1" max="12">
                        </div>
                    </div>
                </div>

                <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
                    <button type="submit" class="btn">📈 Generate Chart</button>
                    <button type="button" class="btn btn-secondary" onclick="clearChart()">🗑️ Clear Chart</button>
                </div>
            </form>
        </div>

        <div id="errorContainer" class="hidden"></div>
        <div id="loadingContainer" class="hidden">
            <div class="loading">
                <h3>📊 Loading statistics data...</h3>
                <p>Please wait while we fetch and process your data.</p>
            </div>
        </div>

        <div id="summaryContainer" class="summary-container hidden">
            <h2>📋 Summary Statistics</h2>
            <div class="summary-grid">
                <div class="summary-card">
                    <h3 id="totalCount">0</h3>
                    <p>Total Count</p>
                </div>
                <div class="summary-card">
                    <h3 id="periodCount">0</h3>
                    <p>Time Periods</p>
                </div>
                <div class="summary-card">
                    <h3 id="averagePerPeriod">0</h3>
                    <p>Average per Period</p>
                </div>
                <div class="summary-card">
                    <h3 id="maxPeriodCount">0</h3>
                    <p>Peak Period Count</p>
                </div>
            </div>
            <div style="margin-top: 20px; text-align: center;">
                <p><strong>Peak Period:</strong> <span id="peakPeriod">-</span></p>
            </div>
        </div>

        <div id="chartContainer" class="chart-container hidden">
            <h2 id="chartTitle">📈 Statistics Chart</h2>
            <div class="chart-wrapper">
                <canvas id="statsChart"></canvas>
            </div>
            <div style="text-align: center; margin-top: 20px;">
                <button type="button" class="btn btn-secondary" onclick="downloadChart()">💾 Download Chart</button>
                <button type="button" class="btn btn-secondary" onclick="toggleChartType()">🔄 Toggle Chart Type</button>
            </div>
        </div>
    </div>

    <script>
        let currentChart = null;
        let currentChartType = 'line';
        let currentData = null;

        // Set current year as default
        document.getElementById('startYear').value = new Date().getFullYear();

        // Handle period change to show/hide conditional fields
        document.getElementById('period').addEventListener('change', function() {
            const period = this.value;
            const weeklyFields = document.getElementById('weeklyFields');
            const monthlyFields = document.getElementById('monthlyFields');

            if (period === 'weekly') {
                weeklyFields.classList.remove('hidden');
                monthlyFields.classList.add('hidden');
                // Clear monthly fields
                document.getElementById('month').value = '';
                document.getElementById('startMonth').value = '';
                document.getElementById('endMonth').value = '';
            } else if (period === 'monthly') {
                monthlyFields.classList.remove('hidden');
                weeklyFields.classList.add('hidden');
                // Clear weekly fields
                document.getElementById('week').value = '';
                document.getElementById('startWeek').value = '';
                document.getElementById('endWeek').value = '';
            } else {
                weeklyFields.classList.add('hidden');
                monthlyFields.classList.add('hidden');
            }
        });

        // Handle form submission
        document.getElementById('statsForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            await loadStatsData();
        });

        async function loadStatsData() {
            const formData = new FormData(document.getElementById('statsForm'));
            const baseUrl = formData.get('baseUrl');

            if (!baseUrl) {
                showError('Please select an Azure Function region');
                return;
            }

            // Show loading
            showLoading();
            hideError();
            hideChart();
            hideSummary();

            try {
                // Build the URL with parameters
                const params = new URLSearchParams();

                // Required parameters
                params.append('apiKey', formData.get('apiKey'));
                params.append('statsType', formData.get('statsType'));
                params.append('period', formData.get('period'));
                params.append('startYear', formData.get('startYear'));
                params.append('aggregation', formData.get('aggregation'));

                // Optional parameters
                if (formData.get('endYear')) params.append('endYear', formData.get('endYear'));

                // Period-specific parameters
                const period = formData.get('period');
                if (period === 'weekly') {
                    if (formData.get('week')) {
                        params.append('week', formData.get('week'));
                    } else {
                        if (formData.get('startWeek')) params.append('startWeek', formData.get('startWeek'));
                        if (formData.get('endWeek')) params.append('endWeek', formData.get('endWeek'));
                    }
                } else if (period === 'monthly') {
                    if (formData.get('month')) {
                        params.append('month', formData.get('month'));
                    } else {
                        if (formData.get('startMonth')) params.append('startMonth', formData.get('startMonth'));
                        if (formData.get('endMonth')) params.append('endMonth', formData.get('endMonth'));
                    }
                }

                const fullUrl = `${baseUrl}?${params.toString()}`;

                // Fetch data
                const response = await fetch(fullUrl);

                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({ error: 'Unknown error' }));
                    throw new Error(errorData.error || `HTTP ${response.status}: ${response.statusText}`);
                }

                const data = await response.json();
                currentData = data;

                // Hide loading
                hideLoading();

                // Display chart and summary
                displayChart(data);
                displaySummary(data);

            } catch (error) {
                hideLoading();
                showError(`Error loading data: ${error.message}`);
                console.error('Error:', error);
            }
        }

        function displayChart(data) {
            const ctx = document.getElementById('statsChart').getContext('2d');

            // Destroy existing chart
            if (currentChart) {
                currentChart.destroy();
            }

            // Update chart title
            document.getElementById('chartTitle').textContent =
                `📈 ${data.statsType} - ${data.period.charAt(0).toUpperCase() + data.period.slice(1)} (${data.aggregation})`;

            // Prepare datasets
            const datasets = data.series.map(series => ({
                label: series.name,
                data: series.data,
                borderColor: series.color,
                backgroundColor: currentChartType === 'bar' ? series.color : series.color + '20',
                fill: currentChartType === 'area',
                tension: 0.4
            }));

            // Create chart
            currentChart = new Chart(ctx, {
                type: currentChartType === 'area' ? 'line' : currentChartType,
                data: {
                    labels: data.labels,
                    datasets: datasets
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        title: {
                            display: true,
                            text: `${data.statsType} Over Time`
                        },
                        legend: {
                            display: data.series.length > 1,
                            position: 'top'
                        }
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: 'Count'
                            }
                        },
                        x: {
                            title: {
                                display: true,
                                text: data.period === 'weekly' ? 'Week' : 'Month'
                            }
                        }
                    },
                    interaction: {
                        intersect: false,
                        mode: 'index'
                    }
                }
            });

            showChart();
        }

        function displaySummary(data) {
            document.getElementById('totalCount').textContent = data.summary.totalCount.toLocaleString();
            document.getElementById('periodCount').textContent = data.summary.periodCount;
            document.getElementById('averagePerPeriod').textContent = data.summary.averagePerPeriod;
            document.getElementById('maxPeriodCount').textContent = data.summary.maxPeriodCount.toLocaleString();
            document.getElementById('peakPeriod').textContent = data.summary.peakPeriod || 'N/A';

            showSummary();
        }

        function toggleChartType() {
            if (!currentData) return;

            const types = ['line', 'bar', 'area'];
            const currentIndex = types.indexOf(currentChartType);
            currentChartType = types[(currentIndex + 1) % types.length];

            displayChart(currentData);
        }

        function downloadChart() {
            if (!currentChart) return;

            const link = document.createElement('a');
            link.download = `stats-chart-${new Date().toISOString().split('T')[0]}.png`;
            link.href = currentChart.toBase64Image();
            link.click();
        }

        function clearChart() {
            if (currentChart) {
                currentChart.destroy();
                currentChart = null;
            }
            currentData = null;
            hideChart();
            hideSummary();
            hideError();
            hideLoading();
        }

        // Utility functions
        function showError(message) {
            const container = document.getElementById('errorContainer');
            container.innerHTML = `<div class="error">${message}</div>`;
            container.classList.remove('hidden');
        }

        function hideError() {
            document.getElementById('errorContainer').classList.add('hidden');
        }

        function showLoading() {
            document.getElementById('loadingContainer').classList.remove('hidden');
        }

        function hideLoading() {
            document.getElementById('loadingContainer').classList.add('hidden');
        }

        function showChart() {
            document.getElementById('chartContainer').classList.remove('hidden');
        }

        function hideChart() {
            document.getElementById('chartContainer').classList.add('hidden');
        }

        function showSummary() {
            document.getElementById('summaryContainer').classList.remove('hidden');
        }

        function hideSummary() {
            document.getElementById('summaryContainer').classList.add('hidden');
        }
    </script>
</body>
</html>
