<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Export Statistics - Atvero Email Filing</title>
    <style>
        * {
            box-sizing: border-box;
            margin: 0;
            padding: 0;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
            background-color: #f5f5f5;
            color: #333;
            line-height: 1.6;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }

        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            border-radius: 10px;
            margin-bottom: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .form-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-row {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
        }

        .form-row .form-group {
            flex: 1;
            margin-bottom: 0;
        }

        label {
            display: block;
            margin-bottom: 5px;
            font-weight: 600;
            color: #555;
        }

        select, input[type="text"], input[type="number"], input[type="password"] {
            width: 100%;
            padding: 12px;
            border: 2px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
            transition: border-color 0.3s ease;
        }

        select:focus, input:focus {
            outline: none;
            border-color: #667eea;
        }

        .help-text {
            font-size: 0.9em;
            color: #666;
            margin-top: 5px;
        }

        .conditional-fields {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 6px;
            border-left: 4px solid #667eea;
        }

        .hidden {
            display: none;
        }

        .submit-section {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
        }

        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
        }

        .btn:active {
            transform: translateY(0);
        }

        .url-preview {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            margin-top: 20px;
            border: 1px solid #ddd;
        }

        .url-preview h4 {
            margin-bottom: 10px;
            color: #555;
        }

        .url-text {
            font-family: 'Courier New', monospace;
            font-size: 14px;
            word-break: break-all;
            background: white;
            padding: 10px;
            border-radius: 4px;
            border: 1px solid #ddd;
        }

        .error {
            background: #fee;
            color: #c33;
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 20px;
            border: 1px solid #fcc;
        }

        .info-box {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }

        .info-box h4 {
            color: #1976d2;
            margin-bottom: 10px;
        }

        @media (max-width: 600px) {
            .form-row {
                flex-direction: column;
                gap: 0;
            }
            
            .container {
                padding: 10px;
            }
            
            .header {
                padding: 20px;
            }
            
            .form-container {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📊 Export Statistics</h1>
            <p>Export statistics data from CMap Mail Filing system as CSV files</p>
        </div>

        <div class="info-box">
            <h4>🔑 API Key Required</h4>
            <p>This function requires authentication. Please enter your API key below. The key should be configured in your Azure Function settings as <code>ExportStatsApiKey</code> or <code>EXPORT_STATS_API_KEY</code>.</p>
        </div>

        <div class="form-container">
            <form id="exportForm">
                <div class="form-group">
                    <label for="baseUrl">Azure Function Region:</label>
                    <select id="baseUrl" name="baseUrl" required>
                        <option value="">Select region...</option>
                        <option value="https://atvero-fa-emailfiling-prod.azurewebsites.net/api/ExportStats">UK (Production)</option>
                        <option value="https://atvero-fa-emailfiling-prod-us-002.azurewebsites.net/api/ExportStats">US (Production)</option>
                    </select>
                    <div class="help-text">Choose the Azure Function region (UK or US)</div>
                </div>

                <div class="form-group">
                    <label for="apiKey">API Key:</label>
                    <input type="password" id="apiKey" name="apiKey" required>
                    <div class="help-text">Your API key for authentication</div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="statsType">Statistics Type:</label>
                        <select id="statsType" name="statsType" required>
                            <option value="">Select statistics type...</option>
                            <option value="UserStats">User Statistics</option>
                            <option value="DomainStats">Domain Statistics</option>
                            <option value="AutoFilingStats">Auto Filing Statistics</option>
                            <option value="UserClickStats">User Click Statistics</option>
                            <option value="ProjectStats">Project Statistics</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="period">Period:</label>
                        <select id="period" name="period" required>
                            <option value="">Select period...</option>
                            <option value="weekly">Weekly</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                </div>

                <div class="form-row">
                    <div class="form-group">
                        <label for="startYear">Start Year:</label>
                        <input type="number" id="startYear" name="startYear" min="2020" max="2030" required>
                        <div class="help-text">4-digit year (e.g., 2024)</div>
                    </div>

                    <div class="form-group">
                        <label for="endYear">End Year (optional):</label>
                        <input type="number" id="endYear" name="endYear" min="2020" max="2030">
                        <div class="help-text">Defaults to start year if not specified</div>
                    </div>
                </div>

                <div id="weeklyFields" class="conditional-fields hidden">
                    <h4>📅 Weekly Range Options</h4>
                    <p class="help-text">Choose either a specific week or a range of weeks (1-53)</p>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="week">Specific Week:</label>
                            <input type="number" id="week" name="week" min="1" max="53">
                            <div class="help-text">Single week number (1-53)</div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="startWeek">Start Week:</label>
                            <input type="number" id="startWeek" name="startWeek" min="1" max="53">
                        </div>
                        <div class="form-group">
                            <label for="endWeek">End Week:</label>
                            <input type="number" id="endWeek" name="endWeek" min="1" max="53">
                        </div>
                    </div>
                </div>

                <div id="monthlyFields" class="conditional-fields hidden">
                    <h4>📅 Monthly Range Options</h4>
                    <p class="help-text">Choose either a specific month or a range of months (1-12)</p>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="month">Specific Month:</label>
                            <input type="number" id="month" name="month" min="1" max="12">
                            <div class="help-text">Single month number (1-12)</div>
                        </div>
                    </div>
                    
                    <div class="form-row">
                        <div class="form-group">
                            <label for="startMonth">Start Month:</label>
                            <input type="number" id="startMonth" name="startMonth" min="1" max="12">
                        </div>
                        <div class="form-group">
                            <label for="endMonth">End Month:</label>
                            <input type="number" id="endMonth" name="endMonth" min="1" max="12">
                        </div>
                    </div>
                </div>

                <div class="submit-section">
                    <button type="submit" class="btn">📥 Export Statistics</button>
                </div>

                <div id="urlPreview" class="url-preview hidden">
                    <h4>🔗 Generated URL:</h4>
                    <div id="urlText" class="url-text"></div>
                </div>
            </form>
        </div>
    </div>

    <script>
        // Set current year as default
        document.getElementById('startYear').value = new Date().getFullYear();

        // Handle period change to show/hide conditional fields
        document.getElementById('period').addEventListener('change', function() {
            const period = this.value;
            const weeklyFields = document.getElementById('weeklyFields');
            const monthlyFields = document.getElementById('monthlyFields');

            if (period === 'weekly') {
                weeklyFields.classList.remove('hidden');
                monthlyFields.classList.add('hidden');
                // Clear monthly fields
                document.getElementById('month').value = '';
                document.getElementById('startMonth').value = '';
                document.getElementById('endMonth').value = '';
            } else if (period === 'monthly') {
                monthlyFields.classList.remove('hidden');
                weeklyFields.classList.add('hidden');
                // Clear weekly fields
                document.getElementById('week').value = '';
                document.getElementById('startWeek').value = '';
                document.getElementById('endWeek').value = '';
            } else {
                weeklyFields.classList.add('hidden');
                monthlyFields.classList.add('hidden');
            }

            updateUrlPreview();
        });

        // Update URL preview when form changes
        document.getElementById('exportForm').addEventListener('input', updateUrlPreview);
        document.getElementById('exportForm').addEventListener('change', updateUrlPreview);

        function updateUrlPreview() {
            const formData = new FormData(document.getElementById('exportForm'));
            const baseUrl = formData.get('baseUrl');

            if (!baseUrl) return;

            const params = new URLSearchParams();

            // Add required parameters
            if (formData.get('apiKey')) params.append('apiKey', formData.get('apiKey'));
            if (formData.get('statsType')) params.append('statsType', formData.get('statsType'));
            if (formData.get('period')) params.append('period', formData.get('period'));
            if (formData.get('startYear')) params.append('startYear', formData.get('startYear'));

            // Add optional parameters
            if (formData.get('endYear')) params.append('endYear', formData.get('endYear'));

            // Add period-specific parameters
            const period = formData.get('period');
            if (period === 'weekly') {
                if (formData.get('week')) {
                    params.append('week', formData.get('week'));
                } else {
                    if (formData.get('startWeek')) params.append('startWeek', formData.get('startWeek'));
                    if (formData.get('endWeek')) params.append('endWeek', formData.get('endWeek'));
                }
            } else if (period === 'monthly') {
                if (formData.get('month')) {
                    params.append('month', formData.get('month'));
                } else {
                    if (formData.get('startMonth')) params.append('startMonth', formData.get('startMonth'));
                    if (formData.get('endMonth')) params.append('endMonth', formData.get('endMonth'));
                }
            }

            const fullUrl = `${baseUrl}?${params.toString()}`;
            document.getElementById('urlText').textContent = fullUrl;
            document.getElementById('urlPreview').classList.remove('hidden');
        }

        // Handle form submission
        document.getElementById('exportForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const formData = new FormData(this);
            const baseUrl = formData.get('baseUrl');

            if (!baseUrl) {
                alert('Please select an Azure Function region');
                return;
            }

            // Build the URL with parameters
            const params = new URLSearchParams();

            // Required parameters
            params.append('apiKey', formData.get('apiKey'));
            params.append('statsType', formData.get('statsType'));
            params.append('period', formData.get('period'));
            params.append('startYear', formData.get('startYear'));

            // Optional parameters
            if (formData.get('endYear')) params.append('endYear', formData.get('endYear'));

            // Period-specific parameters
            const period = formData.get('period');
            if (period === 'weekly') {
                if (formData.get('week')) {
                    params.append('week', formData.get('week'));
                } else {
                    if (formData.get('startWeek')) params.append('startWeek', formData.get('startWeek'));
                    if (formData.get('endWeek')) params.append('endWeek', formData.get('endWeek'));
                }
            } else if (period === 'monthly') {
                if (formData.get('month')) {
                    params.append('month', formData.get('month'));
                } else {
                    if (formData.get('startMonth')) params.append('startMonth', formData.get('startMonth'));
                    if (formData.get('endMonth')) params.append('endMonth', formData.get('endMonth'));
                }
            }

            const fullUrl = `${baseUrl}?${params.toString()}`;

            // Open the URL in a new tab to download the CSV
            window.open(fullUrl, '_blank');
        });

        // Initialize URL preview
        updateUrlPreview();
    </script>
</body>
</html>
