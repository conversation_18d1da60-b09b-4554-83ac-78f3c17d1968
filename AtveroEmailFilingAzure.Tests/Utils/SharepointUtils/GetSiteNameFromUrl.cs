namespace AtveroEmailFilingAzure.Tests.Utils.SharepointUtils;

using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFilingAzure.Services;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Moq;

[TestClass]
public class GetSiteNameFromUrl_Tests
{
    [TestMethod]
    [TestCategory("Unit")]
    public void SiteUrlIsEmpty()
    {
        Result<string> result = AtveroEmailFiling.Utils.SharepointUtils.GetSiteNameFromUrl("");

        Assert.IsTrue(result.IsFailed);
        Assert.AreEqual("Input string cannot be empty.", result.Reasons.First().Message);
    }

    [TestMethod]
    [TestCategory("Unit")]
    public void SiteUrlIsForNormalSite()
    {
        Result<string> result = AtveroEmailFiling.Utils.SharepointUtils.GetSiteNameFromUrl(
            "https://test.sharepoint.com/sites/test"
        );
        Assert.IsTrue(result.IsSuccess);
        Assert.AreEqual("test", result.Value);
    }

    [TestMethod]
    [TestCategory("Unit")]
    public void SiteUrlIsForNoScheme()
    {
        Result<string> result = AtveroEmailFiling.Utils.SharepointUtils.GetSiteNameFromUrl(
            "test.sharepoint.com/sites/test"
        );
        Assert.IsTrue(result.IsFailed);
        Assert.AreEqual(
            "Invalid URL format.test.sharepoint.com/sites/test",
            result.Reasons.First().Message
        );
    }

    [TestMethod]
    [TestCategory("Unit")]
    public void SiteUrlIsForRootSite()
    {
        Result<string> result = AtveroEmailFiling.Utils.SharepointUtils.GetSiteNameFromUrl(
            "https://test.sharepoint.com"
        );

        Assert.IsTrue(result.IsSuccess);
        Assert.AreEqual("", result.Value);
    }

    [TestMethod]
    [TestCategory("Unit")]
    public void SiteUrlIsForInvalidUrl()
    {
        Result<string> result = AtveroEmailFiling.Utils.SharepointUtils.GetSiteNameFromUrl(
            "sausage"
        );

        Assert.IsTrue(result.IsFailed);
        Assert.AreEqual("Invalid URL format.sausage", result.Reasons.First().Message);
    }
}
