using System.Collections.Generic;
using System.Linq;
using System.Net;
using AtveroEmailFiling.Functions;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFilingAzure.Tests.Helpers;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using Moq;

namespace AtveroEmailFilingAzure.Tests.Functions
{
    [TestClass]
    public class ExportStatsTest
    {
        private readonly Mock<ILogger<ExportStats>> _loggerMock;
        private readonly Mock<IConfiguration> _configurationMock;
        private readonly Mock<IAzureTableService> _tableServiceMock;
        private readonly ExportStats _function;

        public ExportStatsTest()
        {
            _loggerMock = new Mock<ILogger<ExportStats>>();
            _configurationMock = new Mock<IConfiguration>();
            _tableServiceMock = new Mock<IAzureTableService>();
            _function = new ExportStats(
                _loggerMock.Object,
                _tableServiceMock.Object,
                _configurationMock.Object
            );
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_ReturnsUnauthorized_WhenApiKeyIsInvalid()
        {
            // Arrange
            var request = new FakeHttpRequestData(
                new Mock<FunctionContext>().Object,
                new Uri(
                    "https://test.com/api/ExportStats?statsType=UserStats&period=weekly&startYear=2024&week=1"
                )
            );

            // Setup configuration to require API key but request doesn't have it
            _configurationMock
                .Setup(config => config["ExportStatsApiKey"])
                .Returns("valid-api-key");

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            Assert.AreEqual(HttpStatusCode.Unauthorized, result.StatusCode);
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_ReturnsBadRequest_WhenRequiredParametersAreMissing()
        {
            // Arrange
            var request = new FakeHttpRequestData(
                new Mock<FunctionContext>().Object,
                new Uri("https://test.com/api/ExportStats?apiKey=valid-api-key") // Missing required parameters
            );

            // Setup configuration with valid API key
            _configurationMock
                .Setup(config => config["ExportStatsApiKey"])
                .Returns("valid-api-key");

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            Assert.AreEqual(HttpStatusCode.BadRequest, result.StatusCode);
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_ReturnsOk_WhenValidWeeklyUserStatsRequest()
        {
            // Arrange
            var request = new FakeHttpRequestData(
                new Mock<FunctionContext>().Object,
                new Uri(
                    "https://test.com/api/ExportStats?statsType=UserStats&period=weekly&startYear=2024&week=1&apiKey=valid-api-key"
                )
            );

            // Setup configuration with valid API key
            _configurationMock
                .Setup(config => config["ExportStatsApiKey"])
                .Returns("valid-api-key");

            var mockWeeklyStats = new List<WeeklyUserStats>
            {
                new()
                {
                    PartitionKey = "2024-01_testdomain.com",
                    RowKey = "<EMAIL>",
                    WeeklyCount = 5,
                },
            };

            _tableServiceMock
                .Setup(service =>
                    service.QueryEntitiesAsync<WeeklyUserStats>(
                        "AAWeeklyUserStats",
                        It.IsAny<string>(),
                        ""
                    )
                )
                .ReturnsAsync(mockWeeklyStats);

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            Assert.AreEqual(HttpStatusCode.OK, result.StatusCode);
            Assert.IsTrue(result.Headers.Contains("Content-Type"));
            Assert.AreEqual("text/csv", result.Headers.GetValues("Content-Type").First());
            Assert.IsTrue(result.Headers.Contains("Content-Disposition"));
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_ReturnsOk_WhenValidMonthlyDomainStatsRequest()
        {
            // Arrange
            var request = new FakeHttpRequestData(
                new Mock<FunctionContext>().Object,
                new Uri(
                    "https://test.com/api/ExportStats?statsType=DomainStats&period=monthly&startYear=2024&month=6&apiKey=valid-api-key"
                )
            );

            // Setup configuration with valid API key
            _configurationMock
                .Setup(config => config["ExportStatsApiKey"])
                .Returns("valid-api-key");

            var mockMonthlyStats = new List<MonthlyDomainStats>
            {
                new()
                {
                    PartitionKey = "2024-06",
                    RowKey = "testdomain.com",
                    MonthlyCount = 25,
                },
            };

            _tableServiceMock
                .Setup(service =>
                    service.QueryEntitiesAsync<MonthlyDomainStats>(
                        "AAMonthlyDomainStats",
                        It.IsAny<string>(),
                        ""
                    )
                )
                .ReturnsAsync(mockMonthlyStats);

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            Assert.AreEqual(HttpStatusCode.OK, result.StatusCode);
            Assert.IsTrue(result.Headers.Contains("Content-Type"));
            Assert.AreEqual("text/csv", result.Headers.GetValues("Content-Type").First());
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_CallsCorrectTable_ForProjectStats()
        {
            // Arrange
            var request = new FakeHttpRequestData(
                new Mock<FunctionContext>().Object,
                new Uri(
                    "https://test.com/api/ExportStats?statsType=ProjectStats&period=weekly&startYear=2024&week=15&apiKey=valid-api-key"
                )
            );

            // Setup configuration with valid API key
            _configurationMock
                .Setup(config => config["ExportStatsApiKey"])
                .Returns("valid-api-key");

            _tableServiceMock
                .Setup(service =>
                    service.QueryEntitiesAsync<WeeklyProjectStats>(
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        ""
                    )
                )
                .ReturnsAsync([]);

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            _tableServiceMock.Verify(
                service =>
                    service.QueryEntitiesAsync<WeeklyProjectStats>(
                        "AAWeeklyProjectStats",
                        It.IsAny<string>(),
                        ""
                    ),
                Times.Once
            );
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_HandlesRangeRequests_Correctly()
        {
            // Arrange
            var request = new FakeHttpRequestData(
                new Mock<FunctionContext>().Object,
                new Uri(
                    "https://test.com/api/ExportStats?statsType=UserStats&period=weekly&startYear=2024&startWeek=1&endWeek=3&apiKey=valid-api-key"
                )
            );

            // Setup configuration with valid API key
            _configurationMock
                .Setup(config => config["ExportStatsApiKey"])
                .Returns("valid-api-key");

            _tableServiceMock
                .Setup(service =>
                    service.QueryEntitiesAsync<WeeklyUserStats>(
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        ""
                    )
                )
                .ReturnsAsync([]);

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            Assert.AreEqual(HttpStatusCode.OK, result.StatusCode);
            // Should call the service 3 times (weeks 1, 2, 3)
            _tableServiceMock.Verify(
                service =>
                    service.QueryEntitiesAsync<WeeklyUserStats>(
                        "AAWeeklyUserStats",
                        It.IsAny<string>(),
                        ""
                    ),
                Times.Exactly(3)
            );
        }
    }
}
