using System.Collections.Generic;
using System.Net;
using System.Threading.Tasks;
using AtveroEmailFiling.Functions;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFilingAzure.Tests.Helpers;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Moq;

namespace AtveroEmailFiling.Tests.Functions
{
    [TestClass]
    public class ExportStatsTest
    {
        private readonly Mock<ILogger<ExportStats>> _loggerMock;
        private readonly Mock<ITokenValidationService> _tokenValidationServiceMock;
        private readonly Mock<IAzureTableService> _tableServiceMock;
        private readonly ExportStats _function;

        public ExportStatsTest()
        {
            _loggerMock = new Mock<ILogger<ExportStats>>();
            _tokenValidationServiceMock = new Mock<ITokenValidationService>();
            _tableServiceMock = new Mock<IAzureTableService>();
            _function = new ExportStats(
                _loggerMock.Object,
                _tableServiceMock.Object,
                _tokenValidationServiceMock.Object
            );
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_ReturnsUnauthorized_WhenTokenIsInvalid()
        {
            // Arrange
            FakeHttpRequestData request = new FakeHttpRequestData(
                (new Mock<FunctionContext>().Object),
                new Uri(
                    "https://test.com/api/ExportStats?statsType=UserStats&period=weekly&startYear=2024&week=1"
                )
            );

            _tokenValidationServiceMock
                .Setup(service => service.ValidateCommonAuthorizationHeaderAsync(request))
                .ReturnsAsync((string)null);

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            Assert.AreEqual(HttpStatusCode.Unauthorized, result.StatusCode);
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_ReturnsBadRequest_WhenRequiredParametersAreMissing()
        {
            // Arrange
            FakeHttpRequestData request = new FakeHttpRequestData(
                (new Mock<FunctionContext>().Object),
                new Uri("https://test.com/api/ExportStats") // Missing required parameters
            );

            _tokenValidationServiceMock
                .Setup(service => service.ValidateCommonAuthorizationHeaderAsync(request))
                .ReturnsAsync("valid-token");

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            Assert.AreEqual(HttpStatusCode.BadRequest, result.StatusCode);
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_ReturnsOk_WhenValidWeeklyUserStatsRequest()
        {
            // Arrange
            FakeHttpRequestData request = new FakeHttpRequestData(
                (new Mock<FunctionContext>().Object),
                new Uri(
                    "https://test.com/api/ExportStats?statsType=UserStats&period=weekly&startYear=2024&week=1"
                )
            );

            _tokenValidationServiceMock
                .Setup(service => service.ValidateCommonAuthorizationHeaderAsync(request))
                .ReturnsAsync("valid-token");

            var mockWeeklyStats = new List<WeeklyUserStats>
            {
                new WeeklyUserStats
                {
                    PartitionKey = "2024-01_testdomain.com",
                    RowKey = "<EMAIL>",
                    WeeklyCount = 5,
                },
            };

            _tableServiceMock
                .Setup(service =>
                    service.QueryEntitiesAsync<WeeklyUserStats>(
                        "AAWeeklyUserStats",
                        It.IsAny<string>(),
                        ""
                    )
                )
                .ReturnsAsync(mockWeeklyStats);

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            Assert.AreEqual(HttpStatusCode.OK, result.StatusCode);
            Assert.IsTrue(result.Headers.Contains("Content-Type"));
            Assert.AreEqual("text/csv", result.Headers.GetValues("Content-Type").First());
            Assert.IsTrue(result.Headers.Contains("Content-Disposition"));
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_ReturnsOk_WhenValidMonthlyDomainStatsRequest()
        {
            // Arrange
            FakeHttpRequestData request = new FakeHttpRequestData(
                (new Mock<FunctionContext>().Object),
                new Uri(
                    "https://test.com/api/ExportStats?statsType=DomainStats&period=monthly&startYear=2024&month=6"
                )
            );

            _tokenValidationServiceMock
                .Setup(service => service.ValidateCommonAuthorizationHeaderAsync(request))
                .ReturnsAsync("valid-token");

            var mockMonthlyStats = new List<MonthlyDomainStats>
            {
                new MonthlyDomainStats
                {
                    PartitionKey = "2024-06",
                    RowKey = "testdomain.com",
                    MonthlyCount = 25,
                },
            };

            _tableServiceMock
                .Setup(service =>
                    service.QueryEntitiesAsync<MonthlyDomainStats>(
                        "AAMonthlyDomainStats",
                        It.IsAny<string>(),
                        ""
                    )
                )
                .ReturnsAsync(mockMonthlyStats);

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            Assert.AreEqual(HttpStatusCode.OK, result.StatusCode);
            Assert.IsTrue(result.Headers.Contains("Content-Type"));
            Assert.AreEqual("text/csv", result.Headers.GetValues("Content-Type").First());
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_CallsCorrectTable_ForProjectStats()
        {
            // Arrange
            FakeHttpRequestData request = new FakeHttpRequestData(
                (new Mock<FunctionContext>().Object),
                new Uri(
                    "https://test.com/api/ExportStats?statsType=ProjectStats&period=weekly&startYear=2024&week=15"
                )
            );

            _tokenValidationServiceMock
                .Setup(service => service.ValidateCommonAuthorizationHeaderAsync(request))
                .ReturnsAsync("valid-token");

            _tableServiceMock
                .Setup(service =>
                    service.QueryEntitiesAsync<WeeklyProjectStats>(
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        ""
                    )
                )
                .ReturnsAsync(new List<WeeklyProjectStats>());

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            _tableServiceMock.Verify(
                service =>
                    service.QueryEntitiesAsync<WeeklyProjectStats>(
                        "AAWeeklyProjectStats",
                        It.IsAny<string>(),
                        ""
                    ),
                Times.Once
            );
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_HandlesRangeRequests_Correctly()
        {
            // Arrange
            FakeHttpRequestData request = new FakeHttpRequestData(
                (new Mock<FunctionContext>().Object),
                new Uri(
                    "https://test.com/api/ExportStats?statsType=UserStats&period=weekly&startYear=2024&startWeek=1&endWeek=3"
                )
            );

            _tokenValidationServiceMock
                .Setup(service => service.ValidateCommonAuthorizationHeaderAsync(request))
                .ReturnsAsync("valid-token");

            _tableServiceMock
                .Setup(service =>
                    service.QueryEntitiesAsync<WeeklyUserStats>(
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        ""
                    )
                )
                .ReturnsAsync(new List<WeeklyUserStats>());

            // Act
            HttpResponseData result = await _function.Run(request);

            // Assert
            Assert.AreEqual(HttpStatusCode.OK, result.StatusCode);
            // Should call the service 3 times (weeks 1, 2, 3)
            _tableServiceMock.Verify(
                service =>
                    service.QueryEntitiesAsync<WeeklyUserStats>(
                        "AAWeeklyUserStats",
                        It.IsAny<string>(),
                        ""
                    ),
                Times.Exactly(3)
            );
        }
    }
}
