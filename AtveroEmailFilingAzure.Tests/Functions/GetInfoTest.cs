using System.Collections.Generic;
using System.Collections.Specialized;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using AtveroEmailFiling.Functions;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using AtveroEmailFilingAzure.Tests.Helpers;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Moq;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Tests.Functions
{
    [TestClass]
    public class GetInfoTest
    {
        private readonly Mock<ILogger<GetInfo>> _loggerMock;
        private readonly Mock<ITokenValidationService> _tokenValidationServiceMock;
        private readonly Mock<IAzureTableService> _tableServiceMock;
        private readonly GetInfo _function;

        public GetInfoTest()
        {
            _loggerMock = new Mock<ILogger<GetInfo>>();
            _tokenValidationServiceMock = new Mock<ITokenValidationService>();
            _tableServiceMock = new Mock<IAzureTableService>();
            _function = new GetInfo(
                _loggerMock.Object,
                _tokenValidationServiceMock.Object,
                _tableServiceMock.Object
            );
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_ReturnsBadRequest_WhenInternetMessageIdIsMissing()
        {
            // Arrange
            FakeHttpRequestData request = new FakeHttpRequestData(
                (new Mock<FunctionContext>().Object),
                new Uri(
                    "https://stackoverflow.com/questions/67937686/testing-an-azure-function-in-net-5"
                )
            );

            CancellationToken cancellationToken = new CancellationToken();
            // Act
            HttpResponseData result = await _function.Run(request, cancellationToken);
            ApiResponse<string> responseBody = await RequestHelpers.ReadApiResponse<string>(result);

            // Assert

            Console.WriteLine("responseBody: " + result);
            Assert.AreEqual(HttpStatusCode.BadRequest, result.StatusCode);
            Assert.AreEqual("internetMessgeId is required.", responseBody.Message);
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_ReturnsBadRequest_WhenTokenDetailsAreNull()
        {
            // Arrange

            FakeHttpRequestData request = new FakeHttpRequestData(
                (new Mock<FunctionContext>().Object),
                new Uri(
                    "https://stackoverflow.com/questions/67937686/testing-an-azure-function-in-net-5?internetMessageId=test-id"
                )
            );

            _tokenValidationServiceMock
                .Setup(service => service.ValidateCommonAuthorizationHeaderAsync(request))
                .ReturnsAsync((string)null);

            CancellationToken cancellationToken = new CancellationToken();

            // Act
            HttpResponseData result = await _function.Run(request, cancellationToken);
            ApiResponse<string> responseBody = await RequestHelpers.ReadApiResponse<string>(result);

            // Assert
            Assert.AreEqual(HttpStatusCode.BadRequest, result.StatusCode);
            Assert.AreEqual(
                "Unable to extract token details from the request.",
                responseBody.Message
            );
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_ReturnsOk_WhenValidRequest()
        {
            // Arrange
            // Arrange

            FakeHttpRequestData request = new FakeHttpRequestData(
                (new Mock<FunctionContext>().Object),
                new Uri(
                    "https://stackoverflow.com/questions/67937686/testing-an-azure-function-in-net-5?internetMessageId=test-id"
                )
            );

            _tokenValidationServiceMock
                .Setup(service => service.ValidateCommonAuthorizationHeaderAsync(request))
                .ReturnsAsync((string)null);

            Models.TokenDetails tokenDetails = new Models.TokenDetails { Upn = "<EMAIL>" };

            _tokenValidationServiceMock
                .Setup(service => service.ValidateCommonAuthorizationHeaderAsync(request))
                .ReturnsAsync("valid-token");

            _tokenValidationServiceMock
                .Setup(service => service.GetUserDetails("valid-token"))
                .Returns(tokenDetails);

            _tableServiceMock
                .Setup(service =>
                    service.QueryEntitiesAsync<FiledEmail>(
                        "FiledEmails",
                        It.IsAny<string>(),
                        It.IsAny<string>()
                    )
                )
                .ReturnsAsync(new List<FiledEmail>());

            CancellationToken cancellationToken = new CancellationToken();

            // Act
            HttpResponseData result = await _function.Run(request, cancellationToken);
            ApiResponse<List<GetInfoResponse>> responseBody = await RequestHelpers.ReadApiResponse<
                List<GetInfoResponse>
            >(result);

            // Assert
            Assert.AreEqual(HttpStatusCode.OK, result.StatusCode);
            Assert.AreEqual(0, responseBody?.Data?.Count);
        }

        [TestMethod]
        [TestCategory("Unit")]
        public async Task Run_Uses_correct_table()
        {
            // Arrange


            FakeHttpRequestData request = new FakeHttpRequestData(
                (new Mock<FunctionContext>().Object),
                new Uri(
                    "https://stackoverflow.com/questions/67937686/testing-an-azure-function-in-net-5?internetMessageId=test-id"
                )
            );

            _tokenValidationServiceMock
                .Setup(service => service.ValidateCommonAuthorizationHeaderAsync(request))
                .ReturnsAsync((string)null);

            Models.TokenDetails tokenDetails = new Models.TokenDetails { Upn = "<EMAIL>" };

            _tokenValidationServiceMock
                .Setup(service => service.ValidateCommonAuthorizationHeaderAsync(request))
                .ReturnsAsync("valid-token");

            _tokenValidationServiceMock
                .Setup(service => service.GetUserDetails("valid-token"))
                .Returns(tokenDetails);

            _tableServiceMock
                .Setup(service =>
                    service.QueryEntitiesAsync<FiledEmail>(
                        It.IsAny<string>(),
                        It.IsAny<string>(),
                        It.IsAny<string>()
                    )
                )
                .ReturnsAsync(new List<FiledEmail>());

            CancellationToken cancellationToken = new CancellationToken();

            // Act
            HttpResponseData result = await _function.Run(request, cancellationToken);
            _tableServiceMock.Verify(service =>
                service.QueryEntitiesAsync<FiledEmail>(
                    "FiledEmails",
                    It.IsAny<string>(),
                    "domaincom"
                )
            );
        }
    }
}
