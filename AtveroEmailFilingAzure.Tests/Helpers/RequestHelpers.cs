using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AtveroEmailFiling.Models.ApiResponses;
using Microsoft.Azure.Functions.Worker.Http;
using Newtonsoft.Json;

namespace AtveroEmailFilingAzure.Tests.Helpers;

public static class RequestHelpers
{
    public static async Task<ApiResponse<T>> ReadApiResponse<T>(HttpResponseData result)
    {
        result.Body.Position = 0;
        StreamReader reader = new StreamReader(result.Body);
        ApiResponse<T> responseBody = JsonConvert.DeserializeObject<ApiResponse<T>>(
            await reader.ReadToEndAsync()
        );
        return responseBody;
    }
}
