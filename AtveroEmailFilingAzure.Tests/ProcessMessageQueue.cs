namespace AtveroEmailFilingAzure.Tests;

using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFilingAzure.Services;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Moq;

[TestClass]
public class ProcessMessageQueue
{
    // private Mock<ITokenValidationService> _mockTokenValidationService;
    // private Mock<IGraphClientService> _mockGraphClientService;
    // private Mock<ILoggerFactory> _mockLoggerFactory;
    // private Mock<ILogger<AuthService>> _mockLogger;
    // private AuthService _authService;

    [TestInitialize]
    public void Setup()
    {
        // _mockTokenValidationService = new Mock<ITokenValidationService>();
        // _mockGraphClientService = new Mock<IGraphClientService>();
        // _mockLoggerFactory = new Mock<ILoggerFactory>();
        // _mockLogger = new Mock<ILogger<AuthService>>();
        // _mockLoggerFactory
        //     .Setup(f => f.CreateLogger(It.IsAny<string>()))
        //     .Returns(_mockLogger.Object);

        // _authService = new AuthService(
        //     _mockTokenValidationService.Object,
        //     _mockGraphClientService.Object,
        //     _mockLoggerFactory.Object
        // );
    }

    [TestMethod]
    [TestCategory("Unit")]
    public async Task TestProcessingQueue()
    {
        // string token = "";
        // Result<GraphServiceClient> graphClientRes = _authService.GetGraphClientFromToken(token);

        // // Assert
        // Assert.IsTrue(graphClientRes.IsSuccess);

        //bool result = await ProcessEmailFiling.ProcessQueuedMessages("<EMAIL>");
        // Assert.IsTrue(result);
    }
}
