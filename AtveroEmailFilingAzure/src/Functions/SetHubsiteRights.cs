using System.Net;
using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.CsomClientService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSCsomClientContextService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SharePoint.Client;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class SetHubsiteRights
    {
        private readonly ILogger<SetHubsiteRights> _logger;
        private readonly ICsomClientContextService _csomClientContextService;
        private readonly ICsomApiClient _csomApiClient;
        private readonly IGraphApiClient _graphApiClient;

        private readonly IGraphClientService _graphClientService;

        private readonly ITokenValidationService _tokenValidationService;

        // To many paramaters, but it is necessary for the function to work
        public SetHubsiteRights(
            ILogger<SetHubsiteRights> logger,
            ITokenValidationService tokenValidationService,
            IGraphClientService graphClientService,
            IGraphApiClient graphApiClient,
            ICsomClientContextService csomClientContextService,
            ICsomApiClient csomApiClient
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _csomClientContextService =
                csomClientContextService
                ?? throw new ArgumentNullException(nameof(csomClientContextService));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _csomApiClient =
                csomApiClient ?? throw new ArgumentNullException(nameof(csomApiClient));

            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));

            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
        }

        [Function("SetHubsiteRights")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            string? sitePath = req.Query["sitePath"];

            if (string.IsNullOrEmpty(sitePath))
            {
                var res = await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Site path is required.",
                    _logger
                );
                return res;
            }

            string? users = req.Query["users"];

            if (string.IsNullOrEmpty(sitePath))
            {
                var res = await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "sitepath is required.",
                    _logger
                );
                return res;
            }

            if (string.IsNullOrEmpty(users))
            {
                var res = await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "users is required.",
                    _logger
                );
                return res;
            }

            string? hostname = null;
            if (!string.IsNullOrEmpty(sitePath))
            {
                hostname = SharepointUtils.GetTenantFromUrl(sitePath);
            }

            if (hostname == null)
            {
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Hostname is null.",
                    _logger
                );
            }

            string adminTenant = $"https://{hostname}-admin.sharepoint.com";

            try
            {
                string? token =
                    await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(req);
                if (string.IsNullOrEmpty(token))
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to get a valid token.",
                        _logger
                    );
                }

                _logger.LogInformation("Token successfully retrieved.");

                TokenDetails tokenDetails = _tokenValidationService.GetUserDetails(token);
                if (tokenDetails.ClientId == null || tokenDetails.ClientSecret == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "ClientId or ClientSecret is null.",
                        _logger
                    );
                }

                ClientContext? clientContext =
                    await _csomClientContextService.GetClientContextAsync(
                        adminTenant,
                        null,
                        token,
                        tokenDetails.ClientId,
                        tokenDetails.ClientSecret
                    );

                if (clientContext == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Failed to create client context.",
                        _logger
                    );
                }

                Result result = Result.Fail("Hubsite rights not set.");
                try
                {
                    _logger.LogInformation("Attempting to set hubsite rights.");
                    result = await _csomApiClient.GrantHubsiteRights(
                        sitePath,
                        users,
                        clientContext
                    );
                    if (result.IsFailed)
                    {
                        _logger.LogInformation(
                            string.Join(",", result.Errors.Select(m => m.Message))
                        );
                        _logger.LogInformation(result.ToString());
                        _logger.LogError("Failed set hubsite rights.");
                    }
                    else
                    {
                        _logger.LogInformation("Successfully set hubsite rights.");
                    }
                }
                catch (InvalidOperationException ex)
                {
                    return await ApiResponseUtility.HandleInternalError(
                        cancellationToken,
                        req,
                        ex,
                        "Error occurred while fetching associated sites.",
                        _logger
                    );
                }
                catch (Exception ex)
                {
                    return await ApiResponseUtility.HandleInternalError(
                        cancellationToken,
                        req,
                        ex,
                        "Unexpected error occurred while fetching associated sites.",
                        _logger
                    );
                }

                _logger.LogInformation("Constructing response.");
                if (result.IsSuccess)
                {
                    return await ApiResponseUtility.CreateSuccessResponse(
                        cancellationToken,
                        req,
                        null as string,
                        ""
                    );
                }
                else
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        string.Join(",", result.Errors.Select(m => m.Message))
                    );
                }
            }
            catch (JsonException jsonEx)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    jsonEx,
                    "JSON serialization error occurred while processing the request.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
