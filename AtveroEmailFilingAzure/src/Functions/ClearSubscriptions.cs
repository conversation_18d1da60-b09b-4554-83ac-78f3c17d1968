using System.Net;
using AtveroEmailFiling.Functions;
using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using Azure.Security.KeyVault.Secrets;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Atvero.Mail
{
    public class ClearSubscriptions
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<EnqueueEmailRequest> _logger;
        private readonly IConfiguration _configuration;

        private readonly IAzureTableService _tableService;
        private readonly IGraphApiClient _graphApiClient;
        private readonly IGraphClientService _graphClientService;

        public ClearSubscriptions(
            IAzureTableService tableService,
            ITokenValidationService tokenValidationService,
            ILogger<EnqueueEmailRequest> logger,
            IConfiguration configuration,
            IGraphApiClient graphApiClient,
            IGraphClientService graphClientService
        )
        {
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));

            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
        }

        [Function("ClearSubscriptions")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to clear user subscriptions {Time}.",
                DateTime.UtcNow
            );

            try
            {
                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);

                if (null == token)
                {
                    SentrySdk.CaptureMessage("Failed to validate authorization token.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to validate authorization token.",
                        _logger
                    );
                }

                AtveroEmailFiling.Models.TokenDetails? tokenDetails =
                    _tokenValidationService.GetUserDetails(token);

                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                string? kvUrl = _configuration["AUTOTOKEN_VAULT_URL"];

                if (kvUrl == null)
                {
                    _logger.LogInformation("AUTOTOKEN_VAULT_URL not configured");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "AUTOTOKEN_VAULT_URL not configured",
                        _logger
                    );
                }

                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                string? sharedMailbox = req.Query["sharedmailbox"];

                string customerDomainRaw = EmailUtils.ExtractDomainFromEmailOrUpn(
                    tokenDetails.Upn,
                    _logger
                );

                string customerDomain = customerDomainRaw;

                string? mappedDomain = await DomainUtils.MapDomain(
                    customerDomainRaw,
                    _tableService
                );
                if (mappedDomain != null)
                {
                    _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                    customerDomain = mappedDomain;
                }

                string escapedEmail = (tokenDetails.Upn ?? "").Replace("'", "''");

                string filter = $"PartitionKey eq '{escapedEmail}'";

                List<MailSubscription> subscriptions =
                    await _tableService.QueryEntitiesAsync<MailSubscription>(
                        "Subscriptions",
                        filter,
                        customerDomain
                    );
                List<MailSubscription> specificSubcriptions = new List<MailSubscription>();

                if (subscriptions.Count > 0)
                {
                    foreach (MailSubscription sub in subscriptions)
                    {
                        // MiniSubscription hub = new MiniSubscription() { subscription = sub.RowKey };

                        if (!string.IsNullOrEmpty(sharedMailbox)) // we want subscriptions for the shared mailbox
                        {
                            if (sub.SharedMailbox != null && sharedMailbox == sub.SharedMailbox)
                            {
                                specificSubcriptions.Add(sub);
                            }
                        }
                        else
                        {
                            // we want subscriptions not tied to a shared mailbox
                            if (string.IsNullOrEmpty(sub.SharedMailbox))
                            {
                                specificSubcriptions.Add(sub);
                            }
                        }
                    }
                }

                foreach (MailSubscription sub in specificSubcriptions)
                {
                    _logger.LogInformation("Removing subscription " + sub.RowKey);
                    try
                    {
                        // unsubscribe in Graph

                        var vaultResult = await WebhookUtils.GetVaultItems(
                            sub.RowKey,
                            kvUrl,
                            _logger,
                            tokenDetails.ClientId,
                            tokenDetails.ClientSecret
                        );

                        if (vaultResult.IsFailed)
                        {
                            _logger.LogError(
                                "Failed to obtain refresh token or shared secret from vault"
                            );
                        }
                        else
                        {
                            (KeyVaultSecret refreshToken, KeyVaultSecret sharedSecret) =
                                vaultResult.Value;

                            Result<TokenResponse> tokenResponse =
                                await WebhookUtils.GetTokenFromCode(
                                    _logger,
                                    refreshToken.Value,
                                    tokenDetails.ClientId,
                                    tokenDetails.ClientSecret,
                                    WebhookUtils.GetScope(
                                        _logger,
                                        _configuration,
                                        tokenDetails.ClientId
                                    )
                                );

                            if (tokenResponse.IsFailed)
                            {
                                _logger.LogError("Failed to generate refresh token");
                            }
                            else
                            {
                                string? accessToken = tokenResponse.Value.access_token;

                                if (accessToken == null)
                                {
                                    _logger.LogInformation(
                                        "Failed to use refresh token to get access token"
                                    );
                                }
                                else
                                {
                                    // use the access token to create a subscription

                                    string bearerToken = $"{accessToken}";

                                    Microsoft.Graph.GraphServiceClient? graphClient =
                                        _graphClientService.GetUserGraphClient(
                                            accessToken,
                                            tokenDetails.ClientId,
                                            tokenDetails.ClientSecret
                                        );

                                    if (graphClient == null)
                                    {
                                        _logger.LogError(
                                            "Failed to use access token to get graph token"
                                        );
                                    }
                                    else
                                    {
                                        _logger.LogInformation(
                                            "Deleting MS Graph WebHook subscription"
                                        );

                                        try
                                        {
                                            await graphClient
                                                .Subscriptions[sub.RowKey]
                                                .DeleteAsync();
                                            _logger.LogInformation(
                                                "MS Graph WebHook subscription deleted"
                                            );
                                        }
                                        catch (Exception)
                                        {
                                            // carry on
                                            _logger.LogInformation(
                                                "Unable to delete subscription with Graph"
                                            );
                                        }

                                        _logger.LogInformation("Removing secrets from vault");

                                        try
                                        {
                                            var vaultResult1 = await WebhookUtils.DeleteVaultItems(
                                                sub.RowKey,
                                                kvUrl,
                                                _logger,
                                                tokenDetails.ClientId,
                                                tokenDetails.ClientSecret
                                            );
                                        }
                                        catch (Exception)
                                        {
                                            _logger.LogInformation(
                                                "Unable to remove items from vault"
                                            );
                                        }

                                        _logger.LogInformation("Remove subscription from table");

                                        // delete from table

                                        await _tableService.DeleteEntityAsync(
                                            "Subscriptions",
                                            sub.PartitionKey,
                                            sub.RowKey,
                                            customerDomain
                                        );

                                        await _tableService.DeleteEntityAsync(
                                            "SharedSecrets",
                                            sub.RowKey,
                                            sub.PartitionKey,
                                            customerDomain
                                        );

                                        _logger.LogInformation("Subscription removed from table");
                                    }
                                }
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Error clearing subscription");
                    }
                }
                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    "",
                    ""
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
