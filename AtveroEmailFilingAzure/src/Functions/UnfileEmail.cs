using System.Net;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class UnfileEmail
    {
        private readonly ILogger<Hubsites> _logger;
        private readonly IGraphClientService _graphClientService;
        private readonly ITokenValidationService _tokenValidationService;
        private readonly IGraphApiClient _graphApiClient;

        private readonly IAzureTableService _tableService;

        public UnfileEmail(
            ILogger<Hubsites> logger,
            ITokenValidationService tokenValidationService,
            IGraphClientService graphClientService,
            IGraphApiClient graphApiClient,
            IAzureTableService tableService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));

            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));
        }

        [Function("UnfileEmail")]
        public async Task<HttpResponseData?> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            try
            {
                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);

                if (null == token)
                {
                    SentrySdk.CaptureMessage("Failed to validate authorization token.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to validate authorization token.",
                        _logger
                    );
                }

                Models.TokenDetails? tokenDetails = _tokenValidationService.GetUserDetails(token);

                string customerDomain = EmailUtils.ExtractDomainFromEmailOrUpn(
                    tokenDetails.Upn,
                    _logger
                );

                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                // Read the request body
                string requestBody;
                using (StreamReader streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                }

                UnfileEmailRequest? unfileEmailRequest =
                    JsonConvert.DeserializeObject<UnfileEmailRequest>(requestBody);

                if (unfileEmailRequest != null)
                {
                    _logger.LogInformation(
                        "unfile drive item in "
                            + unfileEmailRequest.DriveId
                            + " - "
                            + unfileEmailRequest.Id
                            + " - "
                            + unfileEmailRequest.MessageId
                            + " - "
                            + unfileEmailRequest.ConversationId
                            + " - "
                            + unfileEmailRequest.DriveItemId
                    );

                    Microsoft.Graph.GraphServiceClient? graphClient =
                        _graphClientService.GetUserGraphClient(
                            token,
                            tokenDetails.ClientId,
                            tokenDetails.ClientSecret
                        );

                    if (graphClient != null)
                    {
                        _logger.LogInformation("Unfiling email");

                        _logger.LogInformation("Deleting filed");
                        await graphClient
                            .Drives[unfileEmailRequest.DriveId]
                            .Items[unfileEmailRequest.DriveItemId]
                            .DeleteAsync();

                        _logger.LogInformation("Delete the table entries");

                        _logger.LogInformation(
                            "Delete "
                                + unfileEmailRequest.MessageId
                                + " "
                                + unfileEmailRequest.ProjectCode
                        );

                        // Need to delete from domainFiledMails  (PK = msgID, ProjectCoce = project to unfile from)

                        List<FiledEmail> filedEmails =
                            await _tableService.QueryEntitiesAsync<FiledEmail>(
                                "FiledEmails",
                                $"PartitionKey eq '{unfileEmailRequest.MessageId}' and ProjectCode eq '{unfileEmailRequest.ProjectCode}'",
                                customerDomain
                            );

                        // should only be one, but just in case multiple people filed
                        foreach (FiledEmail filedEmail in filedEmails)
                        {
                            _logger.LogInformation(
                                "Removing filed email entry "
                                    + filedEmail.PartitionKey
                                    + " "
                                    + filedEmail.RowKey
                            );
                            // Its rowid is the timestamp we need to run to query for domainTimeFiledEmails

                            // its FiledBy needs to be the email address for using in domainConversations

                            // domainTimeFiledEmails  PK = timestamp, RK = msgID

                            List<TimeFiledEmail> timeFiledEmails =
                                await _tableService.QueryEntitiesAsync<TimeFiledEmail>(
                                    "TimeFiledEmails",
                                    $"PartitionKey eq '{filedEmail.RowKey}' and RowKey eq '{unfileEmailRequest.MessageId}'",
                                    customerDomain
                                );

                            // delete these items

                            foreach (TimeFiledEmail timeFiledEmail in timeFiledEmails)
                            {
                                _logger.LogInformation(
                                    "Removing timeFiled "
                                        + timeFiledEmail.PartitionKey
                                        + " "
                                        + timeFiledEmail.RowKey
                                );
                                await _tableService.DeleteEntityAsync(
                                    "TimeFiledEmails",
                                    timeFiledEmail.PartitionKey,
                                    timeFiledEmail.RowKey,
                                    customerDomain
                                );
                            }

                            // domainConversations PK = filed by, RK = conversation

                            // we may not have all the data we need so it'll be a table scan

                            List<Models.AzureTables.Conversation> filedConversations;

                            if (filedEmail.FiledByEmail != null)
                            {
                                string escapedEmail = filedEmail.FiledByEmail.Replace("'", "''");

                                filedConversations =
                                    await _tableService.QueryEntitiesAsync<Models.AzureTables.Conversation>(
                                        "Conversations",
                                        $"PartitionKey eq '{escapedEmail}' and RowKey eq '{unfileEmailRequest.ConversationId}'",
                                        customerDomain
                                    );
                            }
                            else
                            {
                                // urgh, table scan

                                _logger.LogInformation("Have to perform a table scan");
                                filedConversations =
                                    await _tableService.QueryEntitiesAsync<Models.AzureTables.Conversation>(
                                        "Conversations",
                                        $"RowKey eq '{unfileEmailRequest.ConversationId}'",
                                        customerDomain
                                    );
                            }

                            foreach (
                                Models.AzureTables.Conversation conversation in filedConversations
                            )
                            {
                                _logger.LogInformation(
                                    "Removing conversation "
                                        + conversation.PartitionKey
                                        + " "
                                        + conversation.RowKey
                                );
                                await _tableService.DeleteEntityAsync(
                                    "Conversations",
                                    conversation.PartitionKey,
                                    conversation.RowKey,
                                    customerDomain
                                );
                            }

                            // now clear up the entry

                            await _tableService.DeleteEntityAsync(
                                "FiledEmails",
                                filedEmail.PartitionKey,
                                filedEmail.RowKey,
                                customerDomain
                            );
                        }

                        return await ApiResponseUtility.CreateSuccessResponse<string>(
                            cancellationToken,
                            req,
                            "",
                            "Success"
                        );
                    }
                    else
                    {
                        _logger.LogError("Failed to obtain graph client");
                    }
                }

                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.InternalServerError,
                    "Failed to decode request or process it",
                    _logger
                );
            }
            catch (JsonException jsonEx)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    jsonEx,
                    "JSON serialization error occurred while processing the request.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
