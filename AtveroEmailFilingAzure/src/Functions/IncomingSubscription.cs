using System.Net;
using System.Text;
using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Models.QueueMessages;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using AtveroEmailFilingAzure.Models.ApiRequests;
using Azure.Security.KeyVault.Secrets;
using Azure.Storage.Queues;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Atvero.Mail
{
    public class IncomingSubscription
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<EnqueueEmailRequest> _logger;
        private readonly IConfiguration _configuration;

        private readonly IAzureTableService _tableService;

        private readonly IGraphClientService _graphClientService;

        public IncomingSubscription(
            IAzureTableService tableService,
            ITokenValidationService tokenValidationService,
            ILogger<EnqueueEmailRequest> logger,
            IConfiguration configuration,
            IGraphClientService graphClientService
        )
        {
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));

            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
        }

        [Function("IncomingSubscription")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to process a subscription request at {Time}.",
                DateTime.UtcNow
            );

            // this may just be the check

            if (req.Query.AllKeys.Contains("validationToken"))
            {
                string? token = req.Query.Get("validationToken");

                if (token == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to decode token",
                        _logger
                    );
                }

                string decodedUrl = Uri.UnescapeDataString(token);

                return await ApiResponseUtility.CreateTextSuccessResponse(
                    cancellationToken,
                    req,
                    decodedUrl
                );
            }

            // Create QueueClient
            string? connectionString = _configuration["AzureWebJobsStorage"];

            string queueName = "messageprocessqueue";
            QueueClient regularQueueClient = new QueueClient(connectionString, queueName);
            // Ensure the queue exists (optional)

            Azure.Response? createMessageResult = await regularQueueClient.CreateIfNotExistsAsync();

            string fosQueueName = "fileonsendqueue";
            QueueClient fosQueueClient = new QueueClient(connectionString, fosQueueName);
            // Ensure the queue exists (optional)
            Azure.Response? createFoSResult = await fosQueueClient.CreateIfNotExistsAsync();

            string ffQueueName = "futurefilingqueue";
            var ffQueueClient = new QueueClient(connectionString, ffQueueName);
            // Ensure the queue exists (optional)
            var createFFResult = await ffQueueClient.CreateIfNotExistsAsync();

            string fboQueueName = "filedbyothersqueue";
            var fboQueueClient = new QueueClient(connectionString, fboQueueName);
            // Ensure the queue exists (optional)
            var createFBOResult = await fboQueueClient.CreateIfNotExistsAsync();

            if (req.Method.Equals("POST", StringComparison.OrdinalIgnoreCase))
            {
                string requestBody;
                using (StreamReader streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                }

                SubscriptionRequest? incomingrequest;
                try
                {
                    incomingrequest = JsonConvert.DeserializeObject<SubscriptionRequest>(
                        requestBody
                    );
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Failed to decode JSON request");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to decode request",
                        _logger
                    );
                }

                if (incomingrequest?.request == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to decode request",
                        _logger
                    );
                }

                string? kvUrl = _configuration["AUTOTOKEN_VAULT_URL"];

                if (kvUrl == null)
                {
                    _logger.LogInformation("AUTOTOKEN_VAULT_URL not configured");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "AUTOTOKEN_VAULT_URL not configured",
                        _logger
                    );
                }

                foreach (SubscriptionRequestItem item in incomingrequest.request)
                {
                    ClientState clientState = ClientState.Parse(
                        _logger,
                        item.clientState,
                        _configuration
                    );

                    if (clientState.ClientId == null)
                    {
                        _logger.LogError("Failed to parse client state");
                        continue;
                    }

                    string? clientSecret = WebhookUtils.GetClientSecret(
                        _logger,
                        _configuration,
                        clientState.ClientId
                    );
                    if (clientSecret == null)
                    {
                        _logger.LogError("Failed to get client secret for " + clientState.ClientId);
                        continue;
                    }
                    _logger.LogInformation(
                        "Processing a request on subscription" + item.SubscriptionId
                    );

                    if (item.SubscriptionId == null)
                    {
                        _logger.LogError("Received an item with a null subscription ID");
                        continue;
                    }

                    var vaultResult = await WebhookUtils.GetVaultItems(
                        item.SubscriptionId,
                        kvUrl,
                        _logger,
                        clientState.ClientId,
                        clientSecret
                    );

                    if (vaultResult.IsFailed)
                    {
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.BadRequest,
                            "Failed to obtain refresh token or shared secret from vault",
                            _logger
                        );
                    }

                    (KeyVaultSecret refreshToken, KeyVaultSecret sharedSecret) = vaultResult.Value;

                    // now confirm the shared secret

                    if (sharedSecret.Value != clientState.SharedSecret)
                    {
                        _logger.LogError("Shared secret doesn't match!");
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.BadRequest,
                            "Shared Secrets don't match",
                            _logger
                        );
                    }

                    string? scope = WebhookUtils.GetScope(
                        _logger,
                        _configuration,
                        clientState.ClientId
                    );
                    // get an access token



                    Result<TokenResponse> tokenResponse = await WebhookUtils.GetTokenFromCode(
                        _logger,
                        refreshToken.Value,
                        clientState.ClientId,
                        clientSecret,
                        scope
                    );

                    if (tokenResponse.IsFailed)
                    {
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.BadRequest,
                            "Failed to generate refresh token",
                            _logger
                        );
                    }

                    string? accessToken = tokenResponse.Value.access_token;

                    if (accessToken == null)
                    {
                        _logger.LogInformation("Failed to use refresh token to get access token");

                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.BadRequest,
                            "Failed to use obtain access token",
                            _logger
                        );
                    }

                    // should update the vault with the new refresh token

                    DateTimeOffset? tokenExpiry = refreshToken.Properties.ExpiresOn;

                    _logger.LogInformation("Refresh Token Expires " + tokenExpiry.ToString());

                    // if only 30 days to go, replace it
                    if (DateTime.UtcNow.Add(TimeSpan.FromDays(30)) > tokenExpiry)
                    {
                        string? newRefreshToken = tokenResponse.Value.refresh_token;

                        if (newRefreshToken != null)
                        {
                            _logger.LogInformation("Replacing refresh token to keep it fresh");

                            Result<SecretClient> clientResult = WebhookUtils.GetSecretClient(
                                kvUrl,
                                clientState.ClientId,
                                clientSecret
                            );

                            if (clientResult.IsSuccess)
                            {
                                KeyVaultSecret sessionSecret = new KeyVaultSecret(
                                    WebhookUtils.getRefreshTokenName(item.SubscriptionId),
                                    newRefreshToken
                                );
                                sessionSecret.Properties.ExpiresOn = DateTime.UtcNow.Add(
                                    TimeSpan.FromDays(75)
                                ); // lasts for 90 but we will be cautious
                                await clientResult.Value.SetSecretAsync(sessionSecret);
                            }
                        }
                    }

                    _logger.LogInformation("Ready to queue request!");

                    TokenDetails? tokenDetails = _tokenValidationService.GetUserDetails(
                        accessToken
                    );
                    string customerDomain = EmailUtils.ExtractDomainFromEmailOrUpn(
                        tokenDetails.Upn,
                        _logger
                    );

                    string? mappedDomain = await DomainUtils.MapDomain(
                        customerDomain,
                        _tableService
                    );
                    if (mappedDomain != null)
                    {
                        _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                        customerDomain = mappedDomain;
                    }

                    // this may be a shared mailbox

                    MailSubscription? activeSubscription = null;
                    try
                    {
                        Result<MailSubscription> subscriptionRes =
                            await _tableService.GetEntityAsync<MailSubscription>(
                                "Subscriptions",
                                tokenDetails.Upn ?? "",
                                item.SubscriptionId,
                                customerDomain
                            );

                        if (subscriptionRes.IsSuccess)
                        {
                            activeSubscription = subscriptionRes.Value;
                        }
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(ex, "Failed to look up subscription in table");
                    }

                    if (Environment.GetEnvironmentVariable("ENABLE_FILING_AUTOMATION") == "true")
                    {
                        FileEmailQueueMessage regularMessage = new FileEmailQueueMessage
                        {
                            Token = accessToken,
                            UserId = tokenDetails.Upn ?? "unknown_user",
                            CustomerDomain = customerDomain,
                        };

                        if (
                            activeSubscription != null
                            && !string.IsNullOrEmpty(activeSubscription.SharedMailbox)
                        )
                        {
                            _logger.LogInformation(
                                "Queueing for regular queue processing for Shared Mailbox"
                                    + activeSubscription.SharedMailbox
                            );
                            regularMessage.SharedMailbox = activeSubscription.SharedMailbox;
                        }

                        // Serialize the queue message and base64 encode it
                        System.String? regularMessageString = JsonConvert.SerializeObject(
                            regularMessage
                        );
                        System.String? base64RegularMessage = Convert.ToBase64String(
                            Encoding.UTF8.GetBytes(regularMessageString)
                        );
                        try
                        {
                            await regularQueueClient.SendMessageAsync(base64RegularMessage);
                            _logger.LogInformation("Regular processing queue message enqueued.");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Failed to enqueue message to {QueueName}.",
                                queueName
                            );
                        }
                    }

                    if (Environment.GetEnvironmentVariable("ENABLE_FILE_ON_SEND") == "true")
                    {
                        FileOnSendQueueMessage fosMessage = new FileOnSendQueueMessage
                        {
                            Token = accessToken,
                            UserId = tokenDetails.Upn ?? "unknown_user",
                            CustomerDomain = customerDomain,
                        };

                        if (
                            activeSubscription != null
                            && !string.IsNullOrEmpty(activeSubscription.SharedMailbox)
                        )
                        {
                            _logger.LogInformation(
                                "Queueing for File On Send for Shared Mailbox"
                                    + activeSubscription.SharedMailbox
                            );
                            fosMessage.SharedMailbox = activeSubscription.SharedMailbox;
                        }

                        // Serialize the queue message and base64 encode it
                        System.String? fosMessageString = JsonConvert.SerializeObject(fosMessage);
                        System.String? base64FOSMessage = Convert.ToBase64String(
                            Encoding.UTF8.GetBytes(fosMessageString)
                        );
                        try
                        {
                            await fosQueueClient.SendMessageAsync(base64FOSMessage);
                            _logger.LogInformation(
                                "File on send processing queue message enqueued."
                            );
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Failed to enqueue message to {QueueName}.",
                                fosQueueName
                            );
                        }
                    }

                    if (Environment.GetEnvironmentVariable("ENABLE_FUTURE_FILING") == "true")
                    {
                        var ffMessage = new FutureFilingQueueMessage
                        {
                            Token = accessToken,
                            UserId = tokenDetails.Upn ?? "unknown_user",
                            CustomerDomain = customerDomain,
                        };

                        if (
                            activeSubscription != null
                            && !string.IsNullOrEmpty(activeSubscription.SharedMailbox)
                        )
                        {
                            _logger.LogInformation(
                                "Queueing for Future Filing for Shared Mailbox"
                                    + activeSubscription.SharedMailbox
                            );
                            ffMessage.SharedMailbox = activeSubscription.SharedMailbox;
                        }

                        // Serialize the queue message and base64 encode it
                        var ffMessageString = JsonConvert.SerializeObject(ffMessage);
                        var base64FFMessage = Convert.ToBase64String(
                            Encoding.UTF8.GetBytes(ffMessageString)
                        );
                        try
                        {
                            await ffQueueClient.SendMessageAsync(base64FFMessage);
                            _logger.LogInformation(
                                "Future filing processing queue message enqueued."
                            );
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Failed to enqueue message to {QueueName}.",
                                ffQueueName
                            );
                        }
                    }

                    if (Environment.GetEnvironmentVariable("ENABLE_FILED_BY_OTHERS") == "true")
                    {
                        var fboMessage = new FiledByOthersQueueMessage
                        {
                            Token = accessToken,
                            UserId = tokenDetails.Upn ?? "unknown_user",
                            CustomerDomain = customerDomain,
                        };

                        if (
                            activeSubscription != null
                            && !string.IsNullOrEmpty(activeSubscription.SharedMailbox)
                        )
                        {
                            _logger.LogInformation(
                                "Queueing for FBO for Shared Mailbox"
                                    + activeSubscription.SharedMailbox
                            );
                            fboMessage.SharedMailbox = activeSubscription.SharedMailbox;
                        }

                        // Serialize the queue message and base64 encode it
                        var fboMessageString = JsonConvert.SerializeObject(fboMessage);
                        var base64FBOMessage = Convert.ToBase64String(
                            Encoding.UTF8.GetBytes(fboMessageString)
                        );
                        try
                        {
                            await fboQueueClient.SendMessageAsync(base64FBOMessage);
                            _logger.LogInformation("Message processing queue message enqueued.");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(
                                ex,
                                "Failed to enqueue message to {QueueName}.",
                                fboQueueName
                            );
                        }
                    }
                }

                return await ApiResponseUtility.CreateSuccessResponse<string>(
                    cancellationToken,
                    req,
                    "OK",
                    "Request successfully processed."
                );
            }
            else
            {
                _logger.LogError("Not a POST ");

                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Please POST ",
                    _logger
                );
            }
        }
    }
}
