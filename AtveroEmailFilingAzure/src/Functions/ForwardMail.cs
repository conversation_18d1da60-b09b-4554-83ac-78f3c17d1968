using System.Net;
using System.Text;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class ForwardMail
    {
        private readonly ILogger<Hubsites> _logger;
        private readonly IGraphClientService _graphClientService;
        private readonly ITokenValidationService _tokenValidationService;
        private readonly IGraphApiClient _graphApiClient;

        private readonly IAzureTableService _tableService;

        public ForwardMail(
            ILogger<Hubsites> logger,
            ITokenValidationService tokenValidationService,
            IGraphClientService graphClientService,
            IGraphApiClient graphApiClient,
            IAzureTableService tableService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));

            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));
        }

        [Function("ForwardMail")]
        public async Task<HttpResponseData?> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            try
            {
                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);

                if (null == token)
                {
                    SentrySdk.CaptureMessage("Failed to validate authorization token.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to validate authorization token.",
                        _logger
                    );
                }

                Models.TokenDetails? tokenDetails = _tokenValidationService.GetUserDetails(token);

                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                // Read the request body
                string requestBody;
                using (StreamReader streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                }

                ForwardEmailRequest? forwardEmailRequest =
                    JsonConvert.DeserializeObject<ForwardEmailRequest>(requestBody);

                if (forwardEmailRequest != null)
                {
                    _logger.LogInformation(
                        "forward drive "
                            + forwardEmailRequest.DriveId
                            + " - "
                            + forwardEmailRequest.DriveItemId
                    );

                    Microsoft.Graph.GraphServiceClient? graphClient =
                        _graphClientService.GetUserGraphClient(
                            token,
                            tokenDetails.ClientId,
                            tokenDetails.ClientSecret
                        );

                    if (graphClient != null)
                    {
                        _logger.LogInformation("Downloading content");
                        Stream? stream = await graphClient
                            .Drives[forwardEmailRequest.DriveId]
                            .Items[forwardEmailRequest.DriveItemId]
                            .Content.GetAsync();

                        string content;
                        using (StreamReader streamReader = new StreamReader(stream))
                        {
                            content = await streamReader.ReadToEndAsync();
                        }

                        String? base64Message = Convert.ToBase64String(
                            Encoding.UTF8.GetBytes(content)
                        );

                        Microsoft.Kiota.Abstractions.RequestInformation? requestInformation =
                            graphClient.Me.Messages.ToPostRequestInformation(new Message());
                        requestInformation.Headers.Clear();
                        requestInformation.SetStreamContent(
                            new MemoryStream(Encoding.UTF8.GetBytes(base64Message)),
                            "text/plain"
                        );

                        Message? result = await graphClient.RequestAdapter.SendAsync<Message>(
                            requestInformation,
                            Message.CreateFromDiscriminatorValue
                        );

                        if (result != null)
                        {
                            _logger.LogInformation("Create a forward from the draft");

                            Microsoft.Graph.Me.Messages.Item.CreateForward.CreateForwardPostRequestBody? forwardRequestBody =
                                new Microsoft.Graph.Me.Messages.Item.CreateForward.CreateForwardPostRequestBody
                                {
                                    Comment = " ",
                                };

                            _logger.LogInformation(result.Id);

                            Message? actualforward = await graphClient
                                .Me.Messages[result.Id]
                                .CreateForward.PostAsync(forwardRequestBody);

                            // delete the one we created

                            await graphClient.Me.Messages[result.Id].DeleteAsync();
                        }

                        return await ApiResponseUtility.CreateSuccessResponse<string>(
                            cancellationToken,
                            req,
                            "",
                            "Success"
                        );
                    }
                    else
                    {
                        _logger.LogError("Failed to obtain graph client");
                    }
                }

                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.InternalServerError,
                    "Failed to decode request or process it",
                    _logger
                );
            }
            catch (JsonException jsonEx)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    jsonEx,
                    "JSON serialization error occurred while processing the request.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
