using System.Net;
using System.Runtime.CompilerServices;
using System.Text;
using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.QueueMessages;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using AtveroEmailFilingAzure.Models.ApiRequests;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using Azure.Storage.Queues;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.BusinessData.Runtime;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.SharePoint.Client;
using Newtonsoft.Json;

namespace Atvero.Mail
{
    public class LifeCycleNotifications
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<EnqueueEmailRequest> _logger;
        private readonly IConfiguration _configuration;

        private readonly IAzureTableService _tableService;

        private readonly IGraphClientService _graphClientService;

        public LifeCycleNotifications(
            IAzureTableService tableService,
            ITokenValidationService tokenValidationService,
            ILogger<EnqueueEmailRequest> logger,
            IConfiguration configuration,
            IGraphClientService graphClientService
        )
        {
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));

            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
        }

        private async Task<bool> ReauthorizeSubscription(
            string subscriptionId,
            KeyVaultSecret? refreshToken,
            TokenResponse tr,
            string kvUrl
        )
        {
            _logger.LogInformation("ReauthorizeSubscription");

            // get an access token

            string? accessToken = tr?.access_token;

            if (accessToken == null)
            {
                _logger.LogInformation("Failed to use refresh token to get access token");
                return false;
            }

            TokenDetails tokenDetails = _tokenValidationService.GetUserDetails(accessToken);
            if (tokenDetails.ClientId == null || tokenDetails.ClientSecret == null)
            {
                _logger.LogError("ClientId or ClientSecret is null");
                return false;
            }
            // should update the vault with the new refresh token

            if (refreshToken != null)
            {
                DateTimeOffset? tokenExpiry = refreshToken.Properties.ExpiresOn;

                _logger.LogInformation("Refresh Token Expires " + tokenExpiry.ToString());

                // if only 30 days to go, replace it
                if (DateTime.UtcNow.Add(TimeSpan.FromDays(30)) > tokenExpiry)
                {
                    var clientRes = WebhookUtils.GetSecretClient(
                        kvUrl,
                        tokenDetails.ClientId,
                        tokenDetails.ClientSecret
                    );
                    if (clientRes.IsFailed)
                    {
                        return false;
                    }

                    SecretClient secretClient = clientRes.Value;
                    string? newRefreshToken = tr?.refresh_token;

                    if (newRefreshToken != null)
                    {
                        _logger.LogInformation("Replacing refresh token to keep it fresh");
                        KeyVaultSecret sessionSecret = new KeyVaultSecret(
                            WebhookUtils.getRefreshTokenName(subscriptionId),
                            newRefreshToken
                        );
                        sessionSecret.Properties.ExpiresOn = DateTime.UtcNow.Add(
                            TimeSpan.FromDays(75)
                        ); // lasts for 90 but we will be cautious
                        await secretClient.SetSecretAsync(sessionSecret);
                    }
                }
            }

            Microsoft.Graph.GraphServiceClient? graphClient =
                _graphClientService.GetUserGraphClient(
                    accessToken,
                    tokenDetails.ClientId,
                    tokenDetails.ClientSecret
                );

            if (graphClient == null)
            {
                _logger.LogInformation("Failed to use access token to get graph token");
                return false;
            }

            Microsoft.Graph.Models.Subscription requestBody =
                new Microsoft.Graph.Models.Subscription
                {
                    ExpirationDateTime = DateTime.UtcNow.Add(TimeSpan.FromDays(6)),
                };
            Microsoft.Graph.Models.Subscription? result = await graphClient
                .Subscriptions[subscriptionId]
                .PatchAsync(requestBody);

            _logger.LogInformation("ReauthorizeSubscription completed");

            return true;
        }

        private async Task<Boolean> SubscriptionRemoved(
            string subscriptionId,
            string customerDomain,
            string kvUrl,
            string clientId,
            string clientSecret
        )
        {
            _logger.LogInformation("Removing subscription info from vault");

            var clientRes = WebhookUtils.GetSecretClient(kvUrl, clientId, clientSecret);
            if (clientRes.IsFailed)
            {
                return false;
            }

            SecretClient secretClient = clientRes.Value;

            await secretClient.StartDeleteSecretAsync(
                WebhookUtils.getRefreshTokenName(subscriptionId)
            );
            await secretClient.StartDeleteSecretAsync(
                WebhookUtils.getSharedSecretName(subscriptionId)
            );

            await _tableService.DeleteEntityAsync("", "", "", customerDomain);

            return true;
        }

        [Function("LifeCycleNotifications")]
        public async Task<HttpResponseData?> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to process a LifeCycle Notifications request at {Time}.",
                DateTime.UtcNow
            );

            // this may just be the check

            if (req.Query.AllKeys.Contains("validationToken"))
            {
                string? token = req.Query.Get("validationToken");

                if (token == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to decode token",
                        _logger
                    );
                }

                string decodedUrl = Uri.UnescapeDataString(token);

                return await ApiResponseUtility.CreateTextSuccessResponse(
                    cancellationToken,
                    req,
                    decodedUrl
                );
            }

            // this is a real call back

            if (req.Method.Equals("POST", StringComparison.OrdinalIgnoreCase))
            {
                string requestBody;
                using (StreamReader streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                    // _logger.LogInformation("Request body: {RequestBody}", requestBody);
                }

                LifecycleRequest? incomingrequest;
                try
                {
                    incomingrequest = JsonConvert.DeserializeObject<LifecycleRequest>(requestBody);
                }
                catch (Exception e)
                {
                    _logger.LogError(e, "Failed to decode JSON request");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to decode request",
                        _logger
                    );
                }

                if (incomingrequest?.request == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to decode request",
                        _logger
                    );
                }

                string? kvUrl = _configuration["AUTOTOKEN_VAULT_URL"];

                if (kvUrl == null)
                {
                    _logger.LogInformation("AUTOTOKEN_VAULT_URL not configured");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "AUTOTOKEN_VAULT_URL not configured",
                        _logger
                    );
                }

                // we have the subscription ID

                foreach (LifecycleRequestItem item in incomingrequest.request)
                {
                    ClientState clientState = ClientState.Parse(
                        _logger,
                        item.clientState,
                        _configuration
                    );
                    if (clientState.ClientId == null)
                    {
                        _logger.LogError("Failed to parse client state");
                        continue;
                    }

                    string? clientSecret = WebhookUtils.GetClientSecret(
                        _logger,
                        _configuration,
                        clientState.ClientId
                    );
                    if (clientSecret == null)
                    {
                        _logger.LogError("Failed to get client secret for " + clientState.ClientId);
                        continue;
                    }
                    _logger.LogInformation(
                        "Processing a lifecycle on subscription" + item.SubscriptionId
                    );

                    if (item.SubscriptionId == null)
                    {
                        _logger.LogError("Received a null subscription ID");
                        continue;
                    }

                    // now confirm the shared secret

                    var vaultResult = await WebhookUtils.GetVaultItems(
                        item.SubscriptionId,
                        kvUrl,
                        _logger,
                        clientState.ClientId,
                        clientSecret
                    );

                    if (vaultResult.IsFailed)
                    {
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.BadRequest,
                            "Failed to obtain refresh token or shared secret from vault",
                            _logger
                        );
                    }

                    (KeyVaultSecret refreshToken, KeyVaultSecret sharedSecret) = vaultResult.Value;

                    if (sharedSecret.Value != clientState.SharedSecret)
                    {
                        _logger.LogError("Shared secret doesn't match!");
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.BadRequest,
                            "Shared Secrets don't match",
                            _logger
                        );
                    }
                    string? scope = WebhookUtils.GetScope(
                        _logger,
                        _configuration,
                        clientState.ClientId
                    );
                    Result<TokenResponse> tokenResponse = await WebhookUtils.GetTokenFromCode(
                        _logger,
                        refreshToken.Value,
                        clientState.ClientId,
                        clientSecret,
                        scope
                    );

                    if (tokenResponse.IsFailed)
                    {
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.BadRequest,
                            "Failed to generate refresh token",
                            _logger
                        );
                    }

                    if (item.lifecycleEvent == "reauthorizationRequired")
                    {
                        bool result = await ReauthorizeSubscription(
                            item.SubscriptionId,
                            refreshToken,
                            tokenResponse.Value,
                            kvUrl
                        );

                        if (!result)
                        {
                            return await ApiResponseUtility.CreateErrorResponse<string>(
                                cancellationToken,
                                req,
                                HttpStatusCode.BadRequest,
                                "Failed to reauthorize subscription",
                                _logger
                            );
                        }
                    }

                    if (
                        item.lifecycleEvent == "subscriptionRemoved"
                        && tokenResponse.Value.access_token != null
                    )
                    {
                        TokenDetails? tokenDetails = _tokenValidationService.GetUserDetails(
                            tokenResponse.Value.access_token
                        );

                        string customerDomainRaw =
                            AtveroEmailFiling.Utils.EmailUtils.ExtractDomainFromEmailOrUpn(
                                tokenDetails.Upn,
                                _logger
                            );
                        string customerDomain = customerDomainRaw;

                        string? mappedDomain = await DomainUtils.MapDomain(
                            customerDomainRaw,
                            _tableService
                        );
                        if (mappedDomain != null)
                        {
                            _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                            customerDomain = mappedDomain;
                        }
                        string userName = tokenDetails.Upn ?? "unknown_user";
                        await SubscriptionRemoved(
                            item.SubscriptionId,
                            customerDomain,
                            kvUrl,
                            clientState.ClientId,
                            clientSecret
                        );
                    }

                    if (item.lifecycleEvent == "missed")
                    {
                        _logger.LogInformation(
                            "Told we missed a notification, nothing to do at the moment"
                        );
                    }
                }

                return req.CreateResponse(HttpStatusCode.Accepted);
            }
            else
            {
                _logger.LogError("Not a POST ");
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Please POST ",
                    _logger
                );
            }
        }
    }
}
