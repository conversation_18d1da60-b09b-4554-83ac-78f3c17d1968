// using System.Net;
// using AtveroEmailFiling.Models.AzureTables;
// using AtveroEmailFiling.Services.AzureServices.TableService;
// using Microsoft.Azure.Functions.Worker;
// using Microsoft.Azure.Functions.Worker.Http;
// using Microsoft.Extensions.Logging;
// using Newtonsoft.Json;

// namespace AtveroEmailFiling.Functions
// {
//     public class SetMonthlyFiledCount
//     {
//         private readonly ILogger<SetMonthlyFiledCount> _logger;
//         private readonly IAzureTableService _tableService;

//         public SetMonthlyFiledCount(
//             ILogger<SetMonthlyFiledCount> logger,
//             IAzureTableService tableService
//         )
//         {
//             _logger = logger ?? throw new ArgumentNullException(nameof(logger));
//             _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));
//         }

//         [Function("SetMonthlyFiledCount")]
//         public async Task<HttpResponseData> Run(
//             [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req
//         )
//         {
//             _logger.LogInformation(
//                 "SetMonthlyFiledCount function triggered at {Time}",
//                 DateTime.UtcNow
//             );

//             try
//             {
//                 // Define the tables to process
//                 var tablesToProcess = new[]
//                 {
//                     "AAActiveClickUserStats",
//                     "AAAutoFilingStats",
//                     "AADomainFilingStats",
//                     "AAUserFilingStats",
//                     "AAProjectCustomerStats",
//                     "AAFiledOnSendStats",
//                 };

//                 // Process each table and collect results
//                 var results = new Dictionary<string, (int processed, int updated)>();

//                 foreach (var tableName in tablesToProcess)
//                 {
//                     var result = await ProcessTable(tableName);
//                     results[tableName] = result;
//                 }

//                 var response = req.CreateResponse(HttpStatusCode.OK);
//                 await response.WriteStringAsync(
//                     JsonConvert.SerializeObject(
//                         new
//                         {
//                             success = true,
//                             message = "MonthlyFiledCount updated successfully for all tables",
//                             results = results.ToDictionary(
//                                 kvp => kvp.Key,
//                                 kvp => new
//                                 {
//                                     processed = kvp.Value.processed,
//                                     updated = kvp.Value.updated,
//                                 }
//                             ),
//                             timestamp = DateTime.UtcNow,
//                         }
//                     )
//                 );

//                 return response;
//             }
//             catch (Exception ex)
//             {
//                 _logger.LogError(ex, "Error in SetMonthlyFiledCount function");

//                 var errorResponse = req.CreateResponse(HttpStatusCode.InternalServerError);
//                 await errorResponse.WriteStringAsync(
//                     JsonConvert.SerializeObject(
//                         new
//                         {
//                             success = false,
//                             message = "An error occurred while updating MonthlyFiledCount",
//                             error = ex.Message,
//                             timestamp = DateTime.UtcNow,
//                         }
//                     )
//                 );

//                 return errorResponse;
//             }
//         }

//         private async Task<(int processed, int updated)> ProcessTable(string tableName)
//         {
//             try
//             {
//                 _logger.LogInformation("Processing {TableName}", tableName);

//                 // Query all entries from the specified table
//                 List<UserStat> stats = await _tableService.QueryEntitiesAsync<UserStat>(
//                     tableName,
//                     "", // Empty filter to get all entries
//                     ""
//                 );

//                 _logger.LogInformation(
//                     "Found {Count} entries in {TableName} for processing",
//                     stats.Count,
//                     tableName
//                 );

//                 int updated = 0;

//                 foreach (UserStat stat in stats)
//                 {
//                     // Set MonthlyFiledCount to the same value as FiledCount
//                     stat.MonthlyFiledCount = 0;

//                     // Update the entity
//                     await _tableService.UpdateEntityAsync(tableName, stat, "");
//                     updated++;
//                 }

//                 _logger.LogInformation(
//                     "Updated {Updated} entries in {TableName}",
//                     updated,
//                     tableName
//                 );

//                 return (stats.Count, updated);
//             }
//             catch (Exception ex)
//             {
//                 _logger.LogError(ex, "Error processing {TableName}", tableName);
//                 throw;
//             }
//         }
//     }
// }
