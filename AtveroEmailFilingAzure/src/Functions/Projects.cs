using System.Net;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.CsomClientService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSCsomClientContextService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SharePoint.Client;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class Projects
    {
        private readonly ILogger<Projects> _logger;
        private readonly IGraphClientService _graphClientService;
        private readonly ITokenValidationService _tokenValidationService;

        // private readonly IGraphSiteService _graphService;
        private readonly IGraphApiClient _graphApiClient;

        private readonly ICsomClientContextService _csomClientContextService;
        private readonly ICsomApiClient _csomApiClient;

        public Projects(
            ILogger<Projects> logger,
            ITokenValidationService tokenValidationService,
            IGraphClientService graphClientService,
            // IGraphSiteService graphService,
            IGraphApiClient graphApiClient,
            ICsomClientContextService csomClientContextService,
            ICsomApiClient csomApiClient
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            //    _graphService = graphService ?? throw new ArgumentNullException(nameof(graphService));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _csomApiClient =
                csomApiClient ?? throw new ArgumentNullException(nameof(csomApiClient));
            _csomClientContextService =
                csomClientContextService
                ?? throw new ArgumentNullException(nameof(csomClientContextService));
        }

        [Function("Projects")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            try
            {
                string? sitePath = req.Query["sitePath"];
                string? hubsitePath = req.Query["hubsitePath"];

                if (string.IsNullOrEmpty(sitePath) && string.IsNullOrEmpty(hubsitePath))
                {
                    var res = await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "hubsitePath is required.",
                        _logger
                    );
                    return res;
                }

                if (!string.IsNullOrEmpty(sitePath) && string.IsNullOrEmpty(hubsitePath))
                {
                    hubsitePath = $"/sites/{sitePath}";
                }

                string? search = req.Query["search"];

                var token = await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(
                    req
                );
                if (token != null)
                {
                    var tokenDetails = _tokenValidationService.GetUserDetails(token);
                    _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                    SentrySdk.ConfigureScope(scope =>
                    {
                        scope.User = new SentryUser
                        {
                            Id = tokenDetails.Upn,
                            Email = tokenDetails.Upn,
                        };
                    });
                }

                if (hubsitePath == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Hubsite Path not specified.",
                        _logger
                    );
                }

                string? tenant = null;

                if (hubsitePath.StartsWith("https"))
                {
                    tenant = SharepointUtils.GetFullTenantFromUrl(hubsitePath);
                }
                else
                {
                    Result<string> tenantRes = await FunctionUtils.GetTenantAsync(
                        req,
                        _logger,
                        _tokenValidationService,
                        _graphApiClient,
                        _graphClientService
                    );
                    if (tenantRes.IsFailed)
                    {
                        IEnumerable<Exception> ex = ApiResponseUtility.ExtractExceptionsFromResult(
                            tenantRes.ToResult()
                        );

                        return await ApiResponseUtility.HandleInternalError(
                            cancellationToken,
                            req,
                            ex,
                            "Unable to get SharePoint tenant.",
                            _logger
                        );
                    }
                    tenant = tenantRes.Value;
                }

                // extract the tenant from the hubsite path
                if (tenant == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Unable to extract tenant from hubsite path.",
                        _logger
                    );
                }

                Result<ClientContext> clientContext = await FunctionUtils.GetClientContext(
                    req,
                    tenant,
                    hubsitePath,
                    _tokenValidationService,
                    _csomClientContextService,
                    _logger
                );

                if (clientContext == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Failed to create client context.",
                        _logger
                    );
                }

                List<Project> projects;
                try
                {
                    Result<List<Project>> projectsRes = await _csomApiClient.GetProjectsAsync(
                        search,
                        hubsitePath,
                        clientContext.Value
                    );

                    if (projectsRes.IsSuccess)
                    {
                        projects = projectsRes.Value;
                    }
                    else
                    {
                        IEnumerable<Exception> ex = ApiResponseUtility.ExtractExceptionsFromResult(
                            projectsRes.ToResult()
                        );

                        return await ApiResponseUtility.HandleInternalError(
                            cancellationToken,
                            req,
                            ex,
                            "Could not get projects the hubsite",
                            _logger
                        );
                    }
                }
                catch (InvalidOperationException ex)
                {
                    return await ApiResponseUtility.HandleInternalError(
                        cancellationToken,
                        req,
                        ex,
                        "Error occurred while fetching associated sites.",
                        _logger
                    );
                }
                catch (Exception ex)
                {
                    return await ApiResponseUtility.HandleInternalError(
                        cancellationToken,
                        req,
                        ex,
                        "Unexpected error occurred while fetching associated sites.",
                        _logger
                    );
                }

                // go through and set the path

                foreach (Project project in projects)
                {
                    project.SitePath = $"{tenant}/sites/{project.ProjectCode}";
                }

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    projects,
                    projects.Count > 0
                        ? "Projects retrieved successfully."
                        : "No Projects found for HubSite."
                );
            }
            catch (JsonException jsonEx)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    jsonEx,
                    "JSON serialization error occurred while processing the request.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
