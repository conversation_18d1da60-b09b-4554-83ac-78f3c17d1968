using AtveroEmailFiling.Models.QueueMessages;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.EmailMetadata;
using AtveroEmailFiling.Services.EmailUploading;
using AtveroEmailFiling.Services.FilingStatusCheck;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.GraphApiService.Drives;
using AtveroEmailFiling.Services.GraphApiService.Emails;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using AtveroEmailFilingAzure.Services;
using Azure.Storage.Queues.Models;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using RedLockNet;
using Sentry.Azure.Functions.Worker;

namespace Atvero.Mail
{
    public class ProcessQueuedEmail
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<ProcessQueuedEmail> _logger;
        private readonly IConfiguration _configuration;

        private readonly IAzureTableService _tableService;
        private readonly IGraphDriveService _graphDriveService;

        private readonly IGraphClientService _graphClientService;

        private readonly IGraphApiClient _graphApiClient;

        private readonly IFilingStatusCheckService _filingStatusCheckService;

        private readonly IEmailUploadingService _emailUploadingService;

        private readonly IEmailMetadataService _emailMetadataService;

        private readonly IDistributedLockFactory _lockFactory;

        private readonly IGraphEmailService _graphEmailService;

        public ProcessQueuedEmail(
            IAzureTableService tableService,
            IGraphDriveService graphDriveService,
            IGraphClientService graphClientService,
            IGraphApiClient graphApiClient,
            IFilingStatusCheckService filingStatusCheckService,
            IEmailUploadingService emailUploadingService,
            IEmailMetadataService emailMetadataService,
            ITokenValidationService tokenValidationService,
            ILogger<ProcessQueuedEmail> logger,
            IConfiguration configuration,
            IDistributedLockFactory lockFactory,
            IGraphEmailService graphEmailService
        )
        {
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));

            _graphDriveService =
                graphDriveService ?? throw new ArgumentNullException(nameof(graphDriveService));

            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));

            _filingStatusCheckService =
                filingStatusCheckService
                ?? throw new ArgumentNullException(nameof(filingStatusCheckService));

            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));

            _emailUploadingService =
                emailUploadingService
                ?? throw new ArgumentNullException(nameof(emailUploadingService));

            _emailMetadataService =
                emailMetadataService
                ?? throw new ArgumentNullException(nameof(emailMetadataService));

            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));

            _lockFactory = lockFactory ?? throw new ArgumentNullException(nameof(lockFactory));

            _graphEmailService =
                graphEmailService ?? throw new ArgumentNullException(nameof(graphEmailService));
        }

        [Function("ProcessMessageJobQueue")]
        public async Task ProcessMessageJobQueue(
            [QueueTrigger("messageprocessqueue", Connection = "AzureWebJobsStorage")]
                QueueMessage messageJobQueueMessage
        )
        {
            if (messageJobQueueMessage == null)
            {
                _logger.LogError("Received null QueueMessage in ProcessMessageJobQueue.");
                return;
            }

            // SentrySdk.CaptureMessage("Processing Queued Mails");

            _logger.LogInformation(
                "Received a dequeue request to process queued email at {Time}.",
                DateTime.UtcNow
            );

            FileEmailQueueMessage? jobMessage = DeserializeJobMessage(messageJobQueueMessage);
            if (jobMessage?.Token == null)
            {
                _logger.LogWarning("Failed to deserialize job message.");
                return;
            }

            try
            {
                string token = jobMessage.Token;

                //_logger.LogInformation("Token: " + token);

                DateTime? tokenExpiry = _tokenValidationService.GetTokenExpiryDateTime(token);

                if (tokenExpiry <= DateTime.UtcNow)
                {
                    _logger.LogWarning("Token expired before processing. ");
                    return;
                }

                _logger.LogInformation(
                    "Token Expiry is "
                        + tokenExpiry.ToString()
                        + " compared to now "
                        + DateTime.UtcNow.ToString()
                );

                var tokenDetails = _tokenValidationService.GetUserDetails(token);
                var graphClient = _graphClientService.GetUserGraphClient(
                    token,
                    tokenDetails.ClientId,
                    tokenDetails.ClientSecret
                );

                if (graphClient == null)
                {
                    _logger.LogError("Graph client creation failed ");
                    return;
                }

                string customerDomain = EmailUtils.ExtractDomainFromEmailOrUpn(
                    tokenDetails.Upn,
                    _logger
                );
                string user = tokenDetails.Upn ?? "unknown_user";
                string activeUser = string.IsNullOrEmpty(jobMessage.SharedMailbox)
                    ? user
                    : jobMessage.SharedMailbox;

                _logger.LogInformation("Running as " + activeUser);

                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                // We want just one user process running at a time
                string lockResource = "ProcessMessageQueue-" + activeUser;

                _logger.LogInformation("*** Obtaining lock for " + lockResource);

                // hold the lock for 30 minutes - function will time out after that


                await using (
                    var _lock = await _lockFactory.CreateLockAsync(
                        lockResource,
                        TimeSpan.FromMinutes(30)
                    )
                )
                {
                    if (_lock.IsAcquired)
                    {
                        try
                        {
                            ProcessEmailFiling processor = new ProcessEmailFiling(
                                _logger,
                                _tableService,
                                _graphDriveService,
                                _graphApiClient,
                                _filingStatusCheckService,
                                _emailUploadingService,
                                _emailMetadataService,
                                _graphEmailService,
                                _configuration
                            );

                            await processor.ProcessQueuedMessages(
                                user,
                                customerDomain,
                                graphClient,
                                activeUser
                            );
                            _logger.LogInformation("Job completed for all messages");
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError("Failed whilst processing messages" + ex.Message);
                            SentrySdk.CaptureException(ex);
                        }
                    }
                    else
                    {
                        _logger.LogInformation("Unable to acquire lock.");
                    }
                }
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "Argument exception occurred.");
                SentrySdk.CaptureException(ex);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process queue process request.");
                SentrySdk.CaptureException(ex);
            }
        }

        public FileEmailQueueMessage? DeserializeJobMessage(QueueMessage queueMessage)
        {
            try
            {
                // the message is returned as decoded from the base 64 version stored

                return JsonConvert.DeserializeObject<FileEmailQueueMessage>(
                    queueMessage.MessageText
                );
            }
            catch (JsonException ex)
            {
                SentrySdk.CaptureException(ex);

                _logger.LogError(ex, "Failed to deserialize queue message.");
            }
            catch (Exception ex)
            {
                SentrySdk.CaptureException(ex);

                _logger.LogError(
                    ex,
                    "An unexpected error occurred during message deserialization."
                );
            }

            return null;
        }
    }
}
