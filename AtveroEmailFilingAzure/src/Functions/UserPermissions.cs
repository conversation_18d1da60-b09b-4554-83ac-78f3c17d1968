using System.Net;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.CsomClientService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.GraphApiService.Sites;
using AtveroEmailFiling.Services.MSCsomClientContextService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SharePoint.Client;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class UserPermissions
    {
        private readonly ILogger<UserPermissions> _logger;
        private readonly IGraphClientService _graphClientService;
        private readonly IGraphSiteService _graphService;

        private readonly IGraphApiClient _graphApiClient;

        private readonly ITokenValidationService _tokenValidationService;

        private readonly ICsomClientContextService _csomClientContextService;
        private readonly ICsomApiClient _csomApiClient;

        public UserPermissions(
            ILogger<UserPermissions> logger,
            ITokenValidationService tokenValidationService,
            IGraphClientService graphClientService,
            IGraphSiteService graphService,
            IGraphApiClient graphApiClient,
            ICsomClientContextService csomClientContextService,
            ICsomApiClient csomApiClient
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _graphService = graphService ?? throw new ArgumentNullException(nameof(graphService));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _csomApiClient =
                csomApiClient ?? throw new ArgumentNullException(nameof(csomApiClient));
            _csomClientContextService =
                csomClientContextService
                ?? throw new ArgumentNullException(nameof(csomClientContextService));
        }

        [Function("UserPermissions")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            string? sitePath = req.Query["sitePath"];

            if (string.IsNullOrEmpty(sitePath))
            {
                var res = await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Site path is required.",
                    _logger
                );
                return res;
            }

            string? tenant = null;

            if (sitePath.StartsWith("https"))
            {
                tenant = SharepointUtils.GetFullTenantFromUrl(sitePath);

                _logger.LogInformation("@@@tenant is decoded from the path as" + tenant);
            }
            else
            {
                Result<string> tenantRes = await FunctionUtils.GetTenantAsync(
                    req,
                    _logger,
                    _tokenValidationService,
                    _graphApiClient,
                    _graphClientService
                );

                if (tenantRes.IsSuccess)
                {
                    tenant = tenantRes.Value;
                }
            }

            if (tenant == null)
            {
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Failed to get SharePoint tenant.",
                    _logger
                );
            }

            Result<ClientContext> clientContext = await FunctionUtils.GetClientContext(
                req,
                tenant,
                sitePath,
                _tokenValidationService,
                _csomClientContextService,
                _logger
            );

            if (clientContext == null)
            {
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.InternalServerError,
                    "Failed to create client context.",
                    _logger
                );
            }

            try
            {
                Result<bool> canFile = await _csomApiClient.CanWriteToList(
                    "Filed Email Content",
                    clientContext.Value
                );

                Result<bool> canFileConfidential = await _csomApiClient.CanWriteToList(
                    "Confidential Filed Email Content",
                    clientContext.Value
                );

                if (canFile.IsFailed)
                {
                    // IEnumerable<Exception> ex = ApiResponseUtility.ExtractExceptionsFromResult(
                    //     canFile.ToResult()
                    // );
                    // return await ApiResponseUtility.HandleInternalError(
                    //     response,
                    //     ex,
                    //     "Error occurred while checking user permissions.",
                    //     _logger
                    // );
                    _logger.LogInformation(canFile.ToString());

                    _logger.LogInformation(
                        "Error occurred while checking user permissions - list may be missing."
                    );
                    return await ApiResponseUtility.CreateSuccessResponse(
                        cancellationToken,
                        req,
                        new
                        {
                            Licensed = true,
                            CanFile = false,
                            CanFileConfidential = false,
                        },
                        "Error occurred while checking user permissions - list may be missing."
                    );
                }

                if (canFileConfidential.IsFailed)
                {
                    _logger.LogInformation(
                        "Error occurred while checking confidential user permissions"
                    );
                    _logger.LogInformation(canFileConfidential.ToString());
                }

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    new
                    {
                        Licensed = true,
                        CanFile = canFile.Value,
                        CanFileConfidential = canFileConfidential.IsSuccess
                            && canFileConfidential.Value,
                    },
                    "User permissions retrieved successfully."
                );
            }
            catch (JsonException jsonEx)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    jsonEx,
                    "JSON serialization error occurred while processing the request.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
