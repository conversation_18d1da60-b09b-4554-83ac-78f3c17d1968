using System.Net;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Utils;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class CreateMonthlyUserStats
    {
        private readonly ILogger<CreateMonthlyUserStats> _logger;
        private readonly IAzureTableService _tableService;
        private readonly IConfiguration _configuration;

        public CreateMonthlyUserStats(
            ILogger<CreateMonthlyUserStats> logger,
            IAzureTableService tableService,
            IConfiguration configuration
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        [Function("CreateMonthlyUserStats")]
        public async Task<HttpResponseData> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req
        )
        {
            _logger.LogInformation(
                "CreateMonthlyUserStats function triggered at {Time}",
                DateTime.UtcNow
            );

            try
            {
                // Validate shared API key
                if (
                    !FunctionUtils.ValidateApiKey(
                        req,
                        _configuration,
                        _logger,
                        "CreateMonthlyUserStats"
                    )
                )
                {
                    _logger.LogWarning(
                        "CreateMonthlyUserStats access denied - invalid or missing API key"
                    );
                    var errorResponse = req.CreateResponse(HttpStatusCode.Unauthorized);
                    await errorResponse.WriteStringAsync(
                        JsonConvert.SerializeObject(
                            new { success = false, message = "Unauthorized - Invalid API key" }
                        )
                    );
                    return errorResponse;
                }

                // Process monthly stats
                var monthlyUserResult = await ProcessMonthlyUserFilingStats();
                var monthlyDomainResult = await ProcessMonthlyDomainFilingStats();
                var monthlyClickResult = await ProcessMonthlyUserClickFilingStats();
                var monthlyAutoResult = await ProcessMonthlyAutoFilingStats();
                var monthlyProjectResult = await ProcessMonthlyProjectFilingStats();

                var response = req.CreateResponse(HttpStatusCode.OK);
                await response.WriteStringAsync(
                    JsonConvert.SerializeObject(
                        new
                        {
                            success = true,
                            message = "Monthly user, domain, click, auto, and project filing stats creation completed successfully",
                            monthlyStats = new
                            {
                                userStats = new
                                {
                                    processed = monthlyUserResult.processed,
                                    created = monthlyUserResult.created,
                                },
                                domainStats = new
                                {
                                    processed = monthlyDomainResult.processed,
                                    created = monthlyDomainResult.created,
                                },
                                clickStats = new
                                {
                                    processed = monthlyClickResult.processed,
                                    created = monthlyClickResult.created,
                                },
                                autoStats = new
                                {
                                    processed = monthlyAutoResult.processed,
                                    created = monthlyAutoResult.created,
                                },
                                projectStats = new
                                {
                                    processed = monthlyProjectResult.processed,
                                    created = monthlyProjectResult.created,
                                },
                                totalProcessed = monthlyUserResult.processed
                                    + monthlyDomainResult.processed
                                    + monthlyClickResult.processed
                                    + monthlyAutoResult.processed
                                    + monthlyProjectResult.processed,
                                totalCreated = monthlyUserResult.created
                                    + monthlyDomainResult.created
                                    + monthlyClickResult.created
                                    + monthlyAutoResult.created
                                    + monthlyProjectResult.created,
                                monthPrefix = GetCurrentMonthPrefix(),
                            },
                        }
                    )
                );

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating monthly user stats");

                var errorResponse = req.CreateResponse(HttpStatusCode.InternalServerError);
                await errorResponse.WriteStringAsync(
                    JsonConvert.SerializeObject(
                        new
                        {
                            success = false,
                            message = "Error occurred while creating monthly user stats",
                            error = ex.Message,
                        }
                    )
                );

                return errorResponse;
            }
        }

        private async Task<(int processed, int created)> ProcessMonthlyUserFilingStats()
        {
            try
            {
                _logger.LogInformation("Processing Monthly User Filing stats");

                // Query all entries from AAUserFilingStats
                List<UserStat> userStats = await _tableService.QueryEntitiesAsync<UserStat>(
                    "AAUserFilingStats",
                    "", // Empty filter to get all entries
                    ""
                );

                _logger.LogInformation(
                    "Found {Count} user filing stats for monthly processing",
                    userStats.Count
                );

                int created = 0;
                string monthPrefix = GetCurrentMonthPrefix();

                foreach (UserStat userStat in userStats)
                {
                    string partitionKey = $"{monthPrefix}_{userStat.PartitionKey}";
                    string rowKey = userStat.RowKey;

                    // Check if monthly stats entry already exists
                    var existingStatsResult = await _tableService.GetEntityAsync<MonthlyUserStats>(
                        "AAMonthlyUserStats",
                        partitionKey,
                        rowKey,
                        ""
                    );

                    MonthlyUserStats monthlyStats;
                    if (existingStatsResult.IsSuccess)
                    {
                        // Update existing entry by adding current MonthlyFiledCount
                        monthlyStats = existingStatsResult.Value;
                        monthlyStats.MonthlyCount += userStat.MonthlyFiledCount;
                    }
                    else
                    {
                        // Create new MonthlyUserStats entry using MonthlyCount
                        monthlyStats = new MonthlyUserStats
                        {
                            PartitionKey = partitionKey,
                            RowKey = rowKey,
                            MonthlyCount = userStat.MonthlyFiledCount,
                        };
                    }

                    // Upsert the monthly stats entry
                    await _tableService.UpsertEntityAsync("AAMonthlyUserStats", monthlyStats, "");

                    // Reset MonthlyFiledCount to 0 after processing
                    userStat.MonthlyFiledCount = 0;
                    await _tableService.UpdateEntityAsync("AAUserFilingStats", userStat, "");

                    created++;
                }

                _logger.LogInformation("Created {Created} monthly user stats entries", created);

                return (userStats.Count, created);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing AAUserFilingStats for monthly stats");
                throw;
            }
        }

        private async Task<(int processed, int created)> ProcessMonthlyDomainFilingStats()
        {
            try
            {
                _logger.LogInformation("Processing Monthly Domain Filing stats");

                // Query all entries from AADomainFilingStats
                List<UserStat> domainStats = await _tableService.QueryEntitiesAsync<UserStat>(
                    "AADomainFilingStats",
                    "", // Empty filter to get all entries
                    ""
                );

                _logger.LogInformation(
                    "Found {Count} domain filing stats for monthly processing",
                    domainStats.Count
                );

                int created = 0;
                string monthPrefix = GetCurrentMonthPrefix();

                foreach (UserStat domainStat in domainStats)
                {
                    string partitionKey = monthPrefix; // Just YYYY-MM format
                    string rowKey = domainStat.PartitionKey; // Customer domain from original partition key

                    // Check if monthly stats entry already exists
                    var existingStatsResult =
                        await _tableService.GetEntityAsync<MonthlyDomainStats>(
                            "AAMonthlyDomainStats",
                            partitionKey,
                            rowKey,
                            ""
                        );

                    MonthlyDomainStats monthlyStats;
                    if (existingStatsResult.IsSuccess)
                    {
                        // Update existing entry by adding current MonthlyFiledCount
                        monthlyStats = existingStatsResult.Value;
                        monthlyStats.MonthlyCount += domainStat.MonthlyFiledCount;
                    }
                    else
                    {
                        // Create new MonthlyDomainStats entry using MonthlyCount
                        monthlyStats = new MonthlyDomainStats
                        {
                            PartitionKey = partitionKey,
                            RowKey = rowKey,
                            MonthlyCount = domainStat.MonthlyFiledCount,
                        };
                    }

                    // Upsert the monthly stats entry
                    await _tableService.UpsertEntityAsync("AAMonthlyDomainStats", monthlyStats, "");

                    // Reset MonthlyFiledCount to 0 after processing
                    domainStat.MonthlyFiledCount = 0;
                    await _tableService.UpdateEntityAsync("AADomainFilingStats", domainStat, "");

                    created++;
                }

                _logger.LogInformation("Created {Created} monthly domain stats entries", created);

                return (domainStats.Count, created);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing AADomainFilingStats for monthly stats");
                throw;
            }
        }

        private async Task<(int processed, int created)> ProcessMonthlyUserClickFilingStats()
        {
            try
            {
                _logger.LogInformation("Processing Monthly User Click Filing stats");

                // Query all entries from AAActiveClickUserStats
                List<UserStat> clickStats = await _tableService.QueryEntitiesAsync<UserStat>(
                    "AAActiveClickUserStats",
                    "", // Empty filter to get all entries
                    ""
                );

                _logger.LogInformation(
                    "Found {Count} user click filing stats for monthly processing",
                    clickStats.Count
                );

                int created = 0;
                string monthPrefix = GetCurrentMonthPrefix();

                foreach (UserStat clickStat in clickStats)
                {
                    string partitionKey = $"{monthPrefix}_{clickStat.PartitionKey}";
                    string rowKey = clickStat.RowKey;

                    // Check if monthly stats entry already exists
                    var existingStatsResult =
                        await _tableService.GetEntityAsync<MonthlyUserClickStats>(
                            "AAMonthlyUserClickStats",
                            partitionKey,
                            rowKey,
                            ""
                        );

                    MonthlyUserClickStats monthlyStats;
                    if (existingStatsResult.IsSuccess)
                    {
                        // Update existing entry by adding current MonthlyFiledCount
                        monthlyStats = existingStatsResult.Value;
                        monthlyStats.MonthlyCount += clickStat.MonthlyFiledCount;
                    }
                    else
                    {
                        // Create new MonthlyUserClickStats entry using MonthlyCount
                        monthlyStats = new MonthlyUserClickStats
                        {
                            PartitionKey = partitionKey,
                            RowKey = rowKey,
                            MonthlyCount = clickStat.MonthlyFiledCount,
                        };
                    }

                    // Upsert the monthly stats entry
                    await _tableService.UpsertEntityAsync(
                        "AAMonthlyUserClickStats",
                        monthlyStats,
                        ""
                    );

                    // Reset MonthlyFiledCount to 0 after processing
                    clickStat.MonthlyFiledCount = 0;
                    await _tableService.UpdateEntityAsync("AAActiveClickUserStats", clickStat, "");

                    created++;
                }

                _logger.LogInformation(
                    "Created {Created} monthly user click stats entries",
                    created
                );

                return (clickStats.Count, created);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing AAActiveClickUserStats for monthly stats");
                throw;
            }
        }

        private async Task<(int processed, int created)> ProcessMonthlyAutoFilingStats()
        {
            try
            {
                _logger.LogInformation("Processing Monthly Auto Filing stats");

                // Query all entries from AAAutoFilingStats
                List<UserStat> autoStats = await _tableService.QueryEntitiesAsync<UserStat>(
                    "AAAutoFilingStats",
                    "", // Empty filter to get all entries
                    ""
                );

                _logger.LogInformation(
                    "Found {Count} auto filing stats for monthly processing",
                    autoStats.Count
                );

                int created = 0;
                string monthPrefix = GetCurrentMonthPrefix();

                foreach (UserStat autoStat in autoStats)
                {
                    string partitionKey = $"{monthPrefix}_{autoStat.PartitionKey}";
                    string rowKey = autoStat.RowKey;

                    // Check if monthly stats entry already exists
                    var existingStatsResult =
                        await _tableService.GetEntityAsync<MonthlyAutoFilingStats>(
                            "AAMonthlyAutoFilingStats",
                            partitionKey,
                            rowKey,
                            ""
                        );

                    MonthlyAutoFilingStats monthlyStats;
                    if (existingStatsResult.IsSuccess)
                    {
                        // Update existing entry by adding current MonthlyFiledCount
                        monthlyStats = existingStatsResult.Value;
                        monthlyStats.MonthlyCount += autoStat.MonthlyFiledCount;
                    }
                    else
                    {
                        // Create new MonthlyAutoFilingStats entry using MonthlyCount
                        monthlyStats = new MonthlyAutoFilingStats
                        {
                            PartitionKey = partitionKey,
                            RowKey = rowKey,
                            MonthlyCount = autoStat.MonthlyFiledCount,
                        };
                    }

                    // Upsert the monthly stats entry
                    await _tableService.UpsertEntityAsync(
                        "AAMonthlyAutoFilingStats",
                        monthlyStats,
                        ""
                    );

                    // Reset MonthlyFiledCount to 0 after processing
                    autoStat.MonthlyFiledCount = 0;
                    await _tableService.UpdateEntityAsync("AAAutoFilingStats", autoStat, "");

                    created++;
                }

                _logger.LogInformation(
                    "Created {Created} monthly auto filing stats entries",
                    created
                );

                return (autoStats.Count, created);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing AAAutoFilingStats for monthly stats");
                throw;
            }
        }

        private async Task<(int processed, int created)> ProcessMonthlyProjectFilingStats()
        {
            try
            {
                _logger.LogInformation("Processing Monthly Project Filing stats");

                // Query all entries from AAProjectCustomerStats
                List<UserStat> projectStats = await _tableService.QueryEntitiesAsync<UserStat>(
                    "AAProjectCustomerStats",
                    "", // Empty filter to get all entries
                    ""
                );

                _logger.LogInformation(
                    "Found {Count} project filing stats for monthly processing",
                    projectStats.Count
                );

                int created = 0;
                string monthPrefix = GetCurrentMonthPrefix();

                foreach (UserStat projectStat in projectStats)
                {
                    string partitionKey = $"{monthPrefix}_{projectStat.PartitionKey}";
                    string rowKey = projectStat.RowKey;

                    // Check if monthly stats entry already exists
                    var existingStatsResult =
                        await _tableService.GetEntityAsync<MonthlyProjectStats>(
                            "AAMonthlyProjectStats",
                            partitionKey,
                            rowKey,
                            ""
                        );

                    MonthlyProjectStats monthlyStats;
                    if (existingStatsResult.IsSuccess)
                    {
                        // Update existing entry by adding current MonthlyFiledCount
                        monthlyStats = existingStatsResult.Value;
                        monthlyStats.MonthlyCount += projectStat.MonthlyFiledCount;
                    }
                    else
                    {
                        // Create new MonthlyProjectStats entry using MonthlyCount
                        monthlyStats = new MonthlyProjectStats
                        {
                            PartitionKey = partitionKey,
                            RowKey = rowKey,
                            MonthlyCount = projectStat.MonthlyFiledCount,
                        };
                    }

                    // Upsert the monthly stats entry
                    await _tableService.UpsertEntityAsync(
                        "AAMonthlyProjectStats",
                        monthlyStats,
                        ""
                    );

                    // Reset MonthlyFiledCount to 0 after processing
                    projectStat.MonthlyFiledCount = 0;
                    await _tableService.UpdateEntityAsync(
                        "AAProjectCustomerStats",
                        projectStat,
                        ""
                    );

                    created++;
                }

                _logger.LogInformation("Created {Created} monthly project stats entries", created);

                return (projectStats.Count, created);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing AAProjectCustomerStats for monthly stats");
                throw;
            }
        }

        private static string GetCurrentMonthPrefix()
        {
            DateTime now = DateTime.UtcNow;
            return $"{now.Year}-{now.Month:D2}";
        }
    }
}
