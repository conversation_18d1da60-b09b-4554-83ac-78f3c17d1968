using System.Net;
using System.Text.Json;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace AtveroEmailFiling.Functions
{
    public class GetStatsData
    {
        private readonly ILogger<GetStatsData> _logger;
        private readonly IAzureTableService _tableService;
        private readonly IConfiguration _configuration;

        public GetStatsData(
            ILogger<GetStatsData> logger,
            IAzureTableService tableService,
            IConfiguration configuration
        )
        {
            _logger = logger;
            _tableService = tableService;
            _configuration = configuration;
        }

        [Function("GetStatsData")]
        public async Task<HttpResponseData> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequestData req
        )
        {
            _logger.LogInformation("GetStatsData function triggered at {Time}", DateTime.UtcNow);

            try
            {
                // Validate shared API key
                if (!FunctionUtils.ValidateApiKey(req, _configuration, _logger, "GetStatsData"))
                {
                    _logger.LogWarning("GetStatsData access denied - invalid or missing API key");
                    var errorResponse = req.CreateResponse(HttpStatusCode.Unauthorized);
                    await errorResponse.WriteStringAsync(
                        JsonSerializer.Serialize(new { error = "Unauthorized - Invalid API key" })
                    );
                    return errorResponse;
                }

                // Parse query parameters
                var statsRequest = ParseQueryParameters(req);
                if (statsRequest == null)
                {
                    var errorResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                    await errorResponse.WriteStringAsync(
                        JsonSerializer.Serialize(new { error = "Invalid query parameters" })
                    );
                    return errorResponse;
                }

                // Get statistics data
                var statsData = await GetStatisticsData(statsRequest);

                // Create JSON response
                var response = req.CreateResponse(HttpStatusCode.OK);
                response.Headers.Add("Content-Type", "application/json");
                response.Headers.Add("Access-Control-Allow-Origin", "*"); // Enable CORS for web page

                await response.WriteStringAsync(
                    JsonSerializer.Serialize(
                        statsData,
                        new JsonSerializerOptions
                        {
                            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
                        }
                    )
                );
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting statistics data");
                var errorResponse = req.CreateResponse(HttpStatusCode.InternalServerError);
                await errorResponse.WriteStringAsync(
                    JsonSerializer.Serialize(new { error = "Internal server error" })
                );
                return errorResponse;
            }
        }

        private StatsDataRequest? ParseQueryParameters(HttpRequestData req)
        {
            var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);

            var statsType = query["statsType"];
            var period = query["period"];
            var startYearStr = query["startYear"];
            var endYearStr = query["endYear"];
            var startWeekStr = query["startWeek"];
            var endWeekStr = query["endWeek"];
            var startMonthStr = query["startMonth"];
            var endMonthStr = query["endMonth"];
            var weekStr = query["week"];
            var monthStr = query["month"];
            var aggregation = query["aggregation"] ?? "domain"; // Default to domain aggregation

            // Validate required parameters
            if (
                string.IsNullOrEmpty(statsType)
                || string.IsNullOrEmpty(period)
                || string.IsNullOrEmpty(startYearStr)
            )
            {
                return null;
            }

            if (!int.TryParse(startYearStr, out int startYear))
            {
                return null;
            }

            var request = new StatsDataRequest
            {
                StatsType = statsType,
                Period = period.ToLower(),
                StartYear = startYear,
                EndYear = int.TryParse(endYearStr, out int endYear) ? endYear : startYear,
                Aggregation = aggregation.ToLower(),
            };

            // Parse week/month parameters
            if (period.ToLower() == "weekly")
            {
                if (!string.IsNullOrEmpty(weekStr))
                {
                    if (int.TryParse(weekStr, out int week))
                    {
                        request.StartWeek = week;
                        request.EndWeek = week;
                    }
                }
                else
                {
                    if (int.TryParse(startWeekStr, out int startWeek))
                    {
                        request.StartWeek = startWeek;
                        request.EndWeek = int.TryParse(endWeekStr, out int endWeek)
                            ? endWeek
                            : startWeek;
                    }
                }
            }
            else if (period.ToLower() == "monthly")
            {
                if (!string.IsNullOrEmpty(monthStr))
                {
                    if (int.TryParse(monthStr, out int month))
                    {
                        request.StartMonth = month;
                        request.EndMonth = month;
                    }
                }
                else
                {
                    if (int.TryParse(startMonthStr, out int startMonth))
                    {
                        request.StartMonth = startMonth;
                        request.EndMonth = int.TryParse(endMonthStr, out int endMonth)
                            ? endMonth
                            : startMonth;
                    }
                }
            }

            return request;
        }

        private async Task<StatsDataResponse> GetStatisticsData(StatsDataRequest request)
        {
            // Parse stats type enum
            if (!Enum.TryParse<StatsType>(request.StatsType, true, out var statsType))
            {
                throw new ArgumentException($"Unknown stats type: {request.StatsType}");
            }

            // Build filters and query data
            var filters = BuildFilters(request);
            var allData = new List<StatsDataPoint>();

            foreach (var filter in filters)
            {
                var tableName = GetTableName(statsType, request.Period);
                var periodData = await GetStatsDataGeneric(
                    tableName,
                    filter,
                    request.Period,
                    statsType
                );
                allData.AddRange(periodData);
            }

            // Process data based on aggregation type
            return ProcessStatsData(allData, request);
        }

        private List<string> BuildFilters(StatsDataRequest request)
        {
            var filters = new List<string>();

            // Parse stats type to determine filter pattern
            var isDomainStats =
                Enum.TryParse<StatsType>(request.StatsType, true, out var statsType)
                && statsType == StatsType.DomainStats;

            if (request.Period == "weekly")
            {
                for (int year = request.StartYear; year <= request.EndYear; year++)
                {
                    int startWeek = (year == request.StartYear) ? (request.StartWeek ?? 1) : 1;
                    int endWeek = (year == request.EndYear) ? (request.EndWeek ?? 53) : 53;

                    for (int week = startWeek; week <= endWeek; week++)
                    {
                        string weekPrefix = $"{year:D4}-{week:D2}";

                        // For domain stats, partition key is just YYYY-WW
                        if (isDomainStats)
                        {
                            filters.Add($"PartitionKey eq '{weekPrefix}'");
                        }
                        else
                        {
                            // For other stats, partition key is YYYY-WW_CustomerDomain
                            filters.Add(
                                $"PartitionKey ge '{weekPrefix}_' and PartitionKey lt '{weekPrefix}~'"
                            );
                        }
                    }
                }
            }
            else if (request.Period == "monthly")
            {
                for (int year = request.StartYear; year <= request.EndYear; year++)
                {
                    int startMonth = (year == request.StartYear) ? (request.StartMonth ?? 1) : 1;
                    int endMonth = (year == request.EndYear) ? (request.EndMonth ?? 12) : 12;

                    for (int month = startMonth; month <= endMonth; month++)
                    {
                        string monthPrefix = $"{year:D4}-{month:D2}";

                        // For domain stats, partition key is just YYYY-MM
                        if (isDomainStats)
                        {
                            filters.Add($"PartitionKey eq '{monthPrefix}'");
                        }
                        else
                        {
                            // For other stats, partition key is YYYY-MM_CustomerDomain
                            filters.Add(
                                $"PartitionKey ge '{monthPrefix}_' and PartitionKey lt '{monthPrefix}~'"
                            );
                        }
                    }
                }
            }

            return filters;
        }

        private static string GetTableName(StatsType statsType, string period)
        {
            var prefix = period == "weekly" ? "AAWeekly" : "AAMonthly";

            return statsType switch
            {
                StatsType.UserStats => $"{prefix}UserStats",
                StatsType.DomainStats => $"{prefix}DomainStats",
                StatsType.AutoFilingStats => $"{prefix}AutoFilingStats",
                StatsType.UserClickStats => $"{prefix}UserClickStats",
                StatsType.ProjectStats => $"{prefix}ProjectStats",
                _ => throw new ArgumentException($"Unknown stats type: {statsType}"),
            };
        }

        private async Task<List<StatsDataPoint>> GetStatsDataGeneric(
            string tableName,
            string filter,
            string period,
            StatsType statsType
        )
        {
            if (period == "weekly")
            {
                return await GetWeeklyStatsData(tableName, filter, statsType);
            }
            else
            {
                return await GetMonthlyStatsData(tableName, filter, statsType);
            }
        }

        private async Task<List<StatsDataPoint>> GetWeeklyStatsData(
            string tableName,
            string filter,
            StatsType statsType
        )
        {
            var dataPoints = new List<StatsDataPoint>();

            switch (statsType)
            {
                case StatsType.UserStats:
                    var userStats = await _tableService.QueryEntitiesAsync<WeeklyUserStats>(
                        tableName,
                        filter,
                        ""
                    );
                    dataPoints.AddRange(
                        userStats.Select(s => new StatsDataPoint
                        {
                            Period = s.PartitionKey.Split('_')[0],
                            CustomerDomain =
                                s.PartitionKey.Split('_').Length > 1
                                    ? s.PartitionKey.Split('_')[1]
                                    : "",
                            RowKey = s.RowKey,
                            Count = s.WeeklyCount,
                        })
                    );
                    break;
                case StatsType.DomainStats:
                    var domainStats = await _tableService.QueryEntitiesAsync<WeeklyDomainStats>(
                        tableName,
                        filter,
                        ""
                    );
                    dataPoints.AddRange(
                        domainStats.Select(s => new StatsDataPoint
                        {
                            Period = s.PartitionKey,
                            CustomerDomain = s.RowKey,
                            RowKey = s.RowKey,
                            Count = s.WeeklyCount,
                        })
                    );
                    break;
                case StatsType.AutoFilingStats:
                    var autoStats = await _tableService.QueryEntitiesAsync<WeeklyAutoFilingStats>(
                        tableName,
                        filter,
                        ""
                    );
                    dataPoints.AddRange(
                        autoStats.Select(s => new StatsDataPoint
                        {
                            Period = s.PartitionKey.Split('_')[0],
                            CustomerDomain =
                                s.PartitionKey.Split('_').Length > 1
                                    ? s.PartitionKey.Split('_')[1]
                                    : "",
                            RowKey = s.RowKey,
                            Count = s.WeeklyCount,
                        })
                    );
                    break;
                case StatsType.UserClickStats:
                    var clickStats = await _tableService.QueryEntitiesAsync<WeeklyUserClickStats>(
                        tableName,
                        filter,
                        ""
                    );
                    dataPoints.AddRange(
                        clickStats.Select(s => new StatsDataPoint
                        {
                            Period = s.PartitionKey.Split('_')[0],
                            CustomerDomain =
                                s.PartitionKey.Split('_').Length > 1
                                    ? s.PartitionKey.Split('_')[1]
                                    : "",
                            RowKey = s.RowKey,
                            Count = s.WeeklyCount,
                        })
                    );
                    break;
                case StatsType.ProjectStats:
                    var projectStats = await _tableService.QueryEntitiesAsync<WeeklyProjectStats>(
                        tableName,
                        filter,
                        ""
                    );
                    dataPoints.AddRange(
                        projectStats.Select(s => new StatsDataPoint
                        {
                            Period = s.PartitionKey.Split('_')[0],
                            CustomerDomain =
                                s.PartitionKey.Split('_').Length > 1
                                    ? s.PartitionKey.Split('_')[1]
                                    : "",
                            RowKey = s.RowKey,
                            Count = s.WeeklyCount,
                        })
                    );
                    break;
                default:
                    throw new ArgumentException($"Unknown stats type: {statsType}");
            }

            return dataPoints;
        }

        private async Task<List<StatsDataPoint>> GetMonthlyStatsData(
            string tableName,
            string filter,
            StatsType statsType
        )
        {
            var dataPoints = new List<StatsDataPoint>();

            switch (statsType)
            {
                case StatsType.UserStats:
                    var userStats = await _tableService.QueryEntitiesAsync<MonthlyUserStats>(
                        tableName,
                        filter,
                        ""
                    );
                    dataPoints.AddRange(
                        userStats.Select(s => new StatsDataPoint
                        {
                            Period = s.PartitionKey.Split('_')[0],
                            CustomerDomain =
                                s.PartitionKey.Split('_').Length > 1
                                    ? s.PartitionKey.Split('_')[1]
                                    : "",
                            RowKey = s.RowKey,
                            Count = s.MonthlyCount,
                        })
                    );
                    break;
                case StatsType.DomainStats:
                    var domainStats = await _tableService.QueryEntitiesAsync<MonthlyDomainStats>(
                        tableName,
                        filter,
                        ""
                    );
                    dataPoints.AddRange(
                        domainStats.Select(s => new StatsDataPoint
                        {
                            Period = s.PartitionKey,
                            CustomerDomain = s.RowKey,
                            RowKey = s.RowKey,
                            Count = s.MonthlyCount,
                        })
                    );
                    break;
                case StatsType.AutoFilingStats:
                    var autoStats = await _tableService.QueryEntitiesAsync<MonthlyAutoFilingStats>(
                        tableName,
                        filter,
                        ""
                    );
                    dataPoints.AddRange(
                        autoStats.Select(s => new StatsDataPoint
                        {
                            Period = s.PartitionKey.Split('_')[0],
                            CustomerDomain =
                                s.PartitionKey.Split('_').Length > 1
                                    ? s.PartitionKey.Split('_')[1]
                                    : "",
                            RowKey = s.RowKey,
                            Count = s.MonthlyCount,
                        })
                    );
                    break;
                case StatsType.UserClickStats:
                    var clickStats = await _tableService.QueryEntitiesAsync<MonthlyUserClickStats>(
                        tableName,
                        filter,
                        ""
                    );
                    dataPoints.AddRange(
                        clickStats.Select(s => new StatsDataPoint
                        {
                            Period = s.PartitionKey.Split('_')[0],
                            CustomerDomain =
                                s.PartitionKey.Split('_').Length > 1
                                    ? s.PartitionKey.Split('_')[1]
                                    : "",
                            RowKey = s.RowKey,
                            Count = s.MonthlyCount,
                        })
                    );
                    break;
                case StatsType.ProjectStats:
                    var projectStats = await _tableService.QueryEntitiesAsync<MonthlyProjectStats>(
                        tableName,
                        filter,
                        ""
                    );
                    dataPoints.AddRange(
                        projectStats.Select(s => new StatsDataPoint
                        {
                            Period = s.PartitionKey.Split('_')[0],
                            CustomerDomain =
                                s.PartitionKey.Split('_').Length > 1
                                    ? s.PartitionKey.Split('_')[1]
                                    : "",
                            RowKey = s.RowKey,
                            Count = s.MonthlyCount,
                        })
                    );
                    break;
                default:
                    throw new ArgumentException($"Unknown stats type: {statsType}");
            }

            return dataPoints;
        }

        private StatsDataResponse ProcessStatsData(
            List<StatsDataPoint> allData,
            StatsDataRequest request
        )
        {
            // Generate all period labels for the requested range
            var labels = GeneratePeriodLabels(request);

            // Group data based on aggregation type
            var groupedData = GroupDataByAggregation(allData, request.Aggregation);

            // Generate color palette
            var colors = GenerateColors(groupedData.Count);

            // Create series data
            var series = new List<StatsDataSeries>();
            int colorIndex = 0;

            foreach (var group in groupedData)
            {
                var seriesData = new List<int>();

                foreach (var label in labels)
                {
                    var count = group.Value.Where(d => d.Period == label).Sum(d => d.Count);
                    seriesData.Add(count);
                }

                series.Add(
                    new StatsDataSeries
                    {
                        Name = group.Key,
                        Data = seriesData,
                        Color = colors[colorIndex % colors.Count],
                    }
                );
                colorIndex++;
            }

            // Calculate summary statistics
            var summary = CalculateSummary(allData, labels);

            return new StatsDataResponse
            {
                StatsType = request.StatsType,
                Period = request.Period,
                Aggregation = request.Aggregation,
                Labels = labels,
                Series = series,
                Summary = summary,
            };
        }

        private List<string> GeneratePeriodLabels(StatsDataRequest request)
        {
            var labels = new List<string>();

            if (request.Period == "weekly")
            {
                for (int year = request.StartYear; year <= request.EndYear; year++)
                {
                    int startWeek = (year == request.StartYear) ? (request.StartWeek ?? 1) : 1;
                    int endWeek = (year == request.EndYear) ? (request.EndWeek ?? 53) : 53;

                    for (int week = startWeek; week <= endWeek; week++)
                    {
                        labels.Add($"{year:D4}-{week:D2}");
                    }
                }
            }
            else if (request.Period == "monthly")
            {
                for (int year = request.StartYear; year <= request.EndYear; year++)
                {
                    int startMonth = (year == request.StartYear) ? (request.StartMonth ?? 1) : 1;
                    int endMonth = (year == request.EndYear) ? (request.EndMonth ?? 12) : 12;

                    for (int month = startMonth; month <= endMonth; month++)
                    {
                        labels.Add($"{year:D4}-{month:D2}");
                    }
                }
            }

            return labels;
        }

        private Dictionary<string, List<StatsDataPoint>> GroupDataByAggregation(
            List<StatsDataPoint> allData,
            string aggregation
        )
        {
            return aggregation switch
            {
                "domain" => allData
                    .GroupBy(d => d.CustomerDomain)
                    .ToDictionary(g => g.Key, g => g.ToList()),
                "total" => new Dictionary<string, List<StatsDataPoint>> { { "Total", allData } },
                "individual" => allData
                    .GroupBy(d => $"{d.CustomerDomain} - {d.RowKey}")
                    .ToDictionary(g => g.Key, g => g.ToList()),
                _ => allData
                    .GroupBy(d => d.CustomerDomain)
                    .ToDictionary(g => g.Key, g => g.ToList()),
            };
        }

        private List<string> GenerateColors(int count)
        {
            var baseColors = new List<string>
            {
                "#3498db",
                "#e74c3c",
                "#2ecc71",
                "#f39c12",
                "#9b59b6",
                "#1abc9c",
                "#34495e",
                "#e67e22",
                "#95a5a6",
                "#f1c40f",
            };

            var colors = new List<string>();
            for (int i = 0; i < count; i++)
            {
                colors.Add(baseColors[i % baseColors.Count]);
            }

            return colors;
        }

        private StatsSummary CalculateSummary(List<StatsDataPoint> allData, List<string> labels)
        {
            var totalCount = allData.Sum(d => d.Count);
            var periodCount = labels.Count;
            var averagePerPeriod = periodCount > 0 ? (double)totalCount / periodCount : 0;

            var periodTotals = labels.ToDictionary(
                label => label,
                label => allData.Where(d => d.Period == label).Sum(d => d.Count)
            );

            var maxPeriodCount = periodTotals.Values.DefaultIfEmpty(0).Max();
            var peakPeriod = periodTotals.FirstOrDefault(p => p.Value == maxPeriodCount).Key ?? "";

            return new StatsSummary
            {
                TotalCount = totalCount,
                PeriodCount = periodCount,
                AveragePerPeriod = Math.Round(averagePerPeriod, 2),
                MaxPeriodCount = maxPeriodCount,
                PeakPeriod = peakPeriod,
            };
        }
    }

    public class StatsDataPoint
    {
        public required string Period { get; set; }
        public required string CustomerDomain { get; set; }
        public required string RowKey { get; set; }
        public required int Count { get; set; }
    }

    public class StatsDataRequest
    {
        public required string StatsType { get; set; }
        public required string Period { get; set; }
        public required int StartYear { get; set; }
        public required int EndYear { get; set; }
        public int? StartWeek { get; set; }
        public int? EndWeek { get; set; }
        public int? StartMonth { get; set; }
        public int? EndMonth { get; set; }
        public required string Aggregation { get; set; } = "domain";
    }

    public class StatsDataResponse
    {
        public required string StatsType { get; set; }
        public required string Period { get; set; }
        public required string Aggregation { get; set; }
        public required List<string> Labels { get; set; }
        public required List<StatsDataSeries> Series { get; set; }
        public required StatsSummary Summary { get; set; }
    }

    public class StatsDataSeries
    {
        public required string Name { get; set; }
        public required List<int> Data { get; set; }
        public required string Color { get; set; }
    }

    public class StatsSummary
    {
        public required int TotalCount { get; set; }
        public required int PeriodCount { get; set; }
        public required double AveragePerPeriod { get; set; }
        public required int MaxPeriodCount { get; set; }
        public required string PeakPeriod { get; set; }
    }
}
