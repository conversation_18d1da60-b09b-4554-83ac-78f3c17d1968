using System.Net;
using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.FavService;
using AtveroEmailFiling.Services.TokenValidation;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;

namespace AtveroEmailFiling.Functions
{
    public class Favourites
    {
        private readonly IFavouritesService _favouritesService;
        private readonly ITokenValidationService _tokenValidationService;

        private readonly ILogger<Favourites> _logger;

        public Favourites(
            ILogger<Favourites> logger,
            IFavouritesService favouritesService,
            ITokenValidationService tokenValidationService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _favouritesService =
                favouritesService ?? throw new ArgumentNullException(nameof(favouritesService));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
        }

        [Function("Favourites")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post", "delete")]
                HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            try
            {
                TokenDetails? tokenDetails = null;
                var token = await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(
                    req
                );
                if (token != null)
                {
                    tokenDetails = _tokenValidationService.GetUserDetails(token);
                    _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");
                    SentrySdk.ConfigureScope(scope =>
                    {
                        scope.User = new SentryUser
                        {
                            Id = tokenDetails.Upn,
                            Email = tokenDetails.Upn,
                        };
                    });
                }

                IEnumerable<string>? authenticationHeaders = null;

                if (
                    !cancellationToken.IsCancellationRequested
                    && req.Headers.TryGetValues("Authorization", out var authHeaders)
                )
                {
                    authenticationHeaders = authHeaders;
                }

                // Extract the authorization header
                if (authenticationHeaders == null || !authenticationHeaders.Any())
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to validate authorization token.",
                        _logger
                    );
                }

                _logger.LogInformation("Client ID is " + tokenDetails?.ClientId);

                var bearerToken = authenticationHeaders.First().Split(" ").Last();
                if (
                    !cancellationToken.IsCancellationRequested
                    && req.Method.Equals("POST", StringComparison.OrdinalIgnoreCase)
                )
                {
                    string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                    await _favouritesService.AddFavoritesAsync(
                        "favourites.json",
                        requestBody,
                        bearerToken,
                        tokenDetails?.ClientId,
                        tokenDetails?.ClientSecret
                    );
                    return await ApiResponseUtility.CreateSuccessResponse<string>(
                        cancellationToken,
                        req,
                        "OK",
                        ""
                    );
                }
                else if (
                    !cancellationToken.IsCancellationRequested
                    && req.Method.Equals("DELETE", StringComparison.OrdinalIgnoreCase)
                )
                {
                    string requestBody = await new StreamReader(req.Body).ReadToEndAsync();
                    await _favouritesService.DeleteFavoritesAsync(
                        "favourites.json",
                        requestBody,
                        bearerToken,
                        tokenDetails?.ClientId,
                        tokenDetails?.ClientSecret
                    );
                    return await ApiResponseUtility.CreateSuccessResponse<string>(
                        cancellationToken,
                        req,
                        "OK",
                        ""
                    );
                }
                else if (
                    !cancellationToken.IsCancellationRequested
                    && req.Method.Equals("GET", StringComparison.OrdinalIgnoreCase)
                )
                {
                    string fileContent = await _favouritesService.GetAllFavoritesAsync(
                        "favourites.json",
                        bearerToken,
                        tokenDetails?.ClientId,
                        tokenDetails?.ClientSecret
                    );
                    if (cancellationToken.IsCancellationRequested)
                    {
                        return null;
                    }
                    if (fileContent != null)
                    {
                        var response = req.CreateResponse(HttpStatusCode.OK);
                        response.Headers.Add("Content-Type", "application/json");
                        await response.WriteStringAsync(fileContent);
                        return response;
                    }
                    else
                    {
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.NotFound,
                            "",
                            _logger
                        );
                    }
                }
                else
                {
                    // Unsupported HTTP method
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.MethodNotAllowed,
                        "Unsupported HTTP method.",
                        _logger
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while processing the request.");
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
