using System.Net;
using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class AddHubSite
    {
        private readonly ILogger<AddHubSite> _logger;
        private readonly IAzureTableService _tableService;
        private readonly ITokenValidationService _tokenValidationService;

        public AddHubSite(
            ILogger<AddHubSite> logger,
            IAzureTableService tableService,
            ITokenValidationService tokenValidationService
        )
        {
            _logger = logger;
            _tableService = tableService;
            _tokenValidationService = tokenValidationService;
        }

        [Function("AddHubSite")]
        public async Task<HttpResponseData> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation("AddHubSite function triggered");

            try
            {
                // Validate authorization token
                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);
                if (null == token)
                {
                    SentrySdk.CaptureMessage("Failed to validate authorization token.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to validate authorization token.",
                        _logger
                    );
                }
                TokenDetails tokenDetails = _tokenValidationService.GetUserDetails(token);
                _logger.LogInformation($"Request from user: {tokenDetails.Upn}");

                // Read and parse request body
                string requestBody;
                using (StreamReader streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                }

                if (string.IsNullOrEmpty(requestBody))
                {
                    _logger.LogWarning("Empty request body");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Request body is required",
                        _logger
                    );
                }

                AddHubSiteRequest? addHubSiteRequest;
                try
                {
                    addHubSiteRequest = JsonConvert.DeserializeObject<AddHubSiteRequest>(
                        requestBody
                    );
                }
                catch (JsonException ex)
                {
                    _logger.LogError(ex, "Failed to deserialize request body");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid JSON format in request body",
                        _logger
                    );
                }

                if (addHubSiteRequest == null)
                {
                    _logger.LogWarning("Deserialized request is null");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid request data",
                        _logger
                    );
                }

                if (string.IsNullOrEmpty(addHubSiteRequest.HubSiteName))
                {
                    _logger.LogWarning("HubSiteName is missing from request");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "HubSiteName is required",
                        _logger
                    );
                }

                if (string.IsNullOrEmpty(addHubSiteRequest.HubsiteUrl))
                {
                    _logger.LogWarning("SiteUrl is missing from request");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "SiteUrl is required",
                        _logger
                    );
                }

                // Validate URL format
                if (
                    !Uri.TryCreate(addHubSiteRequest.HubsiteUrl, UriKind.Absolute, out Uri? siteUri)
                    || (siteUri.Scheme != Uri.UriSchemeHttp && siteUri.Scheme != Uri.UriSchemeHttps)
                )
                {
                    _logger.LogWarning($"Invalid SiteUrl format: {addHubSiteRequest.HubsiteUrl}");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "SiteUrl must be a valid HTTP or HTTPS URL",
                        _logger
                    );
                }

                // Extract customer domain from token if not provided or validate if provided
                string customerDomainFromToken = EmailUtils.ExtractDomainFromEmailOrUpn(
                    tokenDetails.Upn,
                    _logger
                );

                // Apply domain mapping if exists
                string? mappedDomain = await DomainUtils.MapDomain(
                    customerDomainFromToken,
                    _tableService
                );
                if (string.IsNullOrEmpty(mappedDomain))
                {
                    mappedDomain = customerDomainFromToken;
                }
                string effectiveCustomerDomain = mappedDomain ?? customerDomainFromToken;

                // Check if hub site already exists
                try
                {
                    Result<HubSite> existingHubSite = await _tableService.GetEntityAsync<HubSite>(
                        "HubSites",
                        mappedDomain, // null check done above
                        addHubSiteRequest.HubSiteName,
                        "" // global table
                    );

                    if (existingHubSite.IsSuccess)
                    {
                        _logger.LogWarning(
                            $"Hub site {addHubSiteRequest.HubSiteName} already exists for domain {mappedDomain}"
                        );
                        return await ApiResponseUtility.CreateSuccessResponse(
                            cancellationToken,
                            req,
                            new
                            {
                                CustomerDomain = existingHubSite.Value.PartitionKey,
                                HubSiteName = existingHubSite.Value.RowKey,
                                SiteUrl = existingHubSite.Value.SiteUrl,
                                CustomTags = existingHubSite.Value.CustomTags,
                            },
                            "Hub site added successfully"
                        );
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error checking for existing hub site");
                    // Continue with creation as the error might be due to entity not existing
                }

                // Create new hub site entity
                HubSite hubSiteEntity = new()
                {
                    PartitionKey = mappedDomain,
                    RowKey = addHubSiteRequest.HubSiteName,
                    SiteUrl = addHubSiteRequest.HubsiteUrl,
                    CustomTags = false,
                };

                // Add to table
                try
                {
                    await _tableService.AddEntityAsync(
                        "HubSites",
                        hubSiteEntity,
                        "" // global table
                    );

                    _logger.LogInformation(
                        $"Successfully added hub site {addHubSiteRequest.HubSiteName} for domain ${mappedDomain}"
                    );

                    return await ApiResponseUtility.CreateSuccessResponse(
                        cancellationToken,
                        req,
                        new
                        {
                            CustomerDomain = hubSiteEntity.PartitionKey,
                            HubSiteName = hubSiteEntity.RowKey,
                            SiteUrl = hubSiteEntity.SiteUrl,
                            CustomTags = hubSiteEntity.CustomTags,
                            Timestamp = hubSiteEntity.Timestamp,
                        },
                        "Hub site added successfully"
                    );
                }
                catch (Exception ex)
                {
                    _logger.LogError(
                        ex,
                        $"Failed to add hub site {addHubSiteRequest.HubSiteName} to table"
                    );
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Failed to add hub site to database",
                        _logger
                    );
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in AddHubSite function");
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while adding the hub site",
                    _logger
                );
            }
        }
    }
}
