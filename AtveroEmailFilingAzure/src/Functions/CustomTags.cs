using System.Net;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    class MiniTag
    {
        public required string Name { get; set; }
        public required string Colour { get; set; }
        public required string BackgroundColour { get; set; }
        public int Order { get; set; }
    }

    public class CustomTags
    {
        private readonly ILogger<CustomTag> _logger;
        private readonly IGraphClientService _graphClientService;
        private readonly ITokenValidationService _tokenValidationService;
        private readonly IGraphApiClient _graphApiClient;
        private readonly IAzureTableService _tableService;

        public CustomTags(
            ILogger<CustomTag> logger,
            ITokenValidationService tokenValidationService,
            IGraphClientService graphClientService,
            IGraphApiClient graphApiClient,
            IAzureTableService tableService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));

            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));
        }

        [Function("customtags")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            try
            {
                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);

                if (null == token)
                {
                    SentrySdk.CaptureMessage("Failed to validate authorization token.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to validate authorization token.",
                        _logger
                    );
                }

                var tokenDetails = _tokenValidationService.GetUserDetails(token);

                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                // First try and get a list of the tags from an Azure table

                bool tagsFromTable = false;

                List<MiniTag> tags = new List<MiniTag>();

                try
                {
                    string customerDomain = EmailUtils.ExtractDomainFromEmailOrUpn(
                        tokenDetails.Upn,
                        _logger
                    );

                    string? mappedDomain = await DomainUtils.MapDomain(
                        customerDomain,
                        _tableService
                    );
                    if (mappedDomain != null)
                    {
                        _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                        customerDomain = mappedDomain;
                    }

                    List<AtveroEmailFiling.Models.AzureTables.CustomTag> TagEntries =
                        await _tableService.QueryEntitiesAsync<CustomTag>(
                            "CustomTags",
                            "",
                            customerDomain
                        );

                    if (TagEntries.Count > 0)
                    {
                        foreach (CustomTag tag in TagEntries)
                        {
                            MiniTag mtag = new MiniTag()
                            {
                                Name = tag.PartitionKey,
                                Colour = tag.Colour,
                                BackgroundColour = tag.BackgroundColour,
                                Order = tag.Order,
                            };

                            tags.Add(mtag);
                        }
                    }

                    tagsFromTable = true;
                }
                catch (Exception ex)
                {
                    _logger.LogInformation("Not able to get hubsites from table");
                    _logger.LogInformation(ex.Message);
                }

                if (!tagsFromTable)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Unexpected error occurred while fetching custom tags",
                        _logger
                    );
                }

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    tags,
                    ""
                );
            }
            catch (JsonException jsonEx)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    jsonEx,
                    "JSON serialization error occurred while processing the request.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
