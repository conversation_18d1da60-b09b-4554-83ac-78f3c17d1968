using System.Net;
using System.Text;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Models.QueueMessages;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.GraphApiService.Emails;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using Azure.Storage.Queues;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Atvero.Mail
{
    public class EnqueueEmailRequest
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<EnqueueEmailRequest> _logger;
        private readonly IConfiguration _configuration;
        private readonly IGraphEmailService _graphEmailService;
        private readonly IGraphClientService _graphClientService;

        private readonly IAzureTableService _tableService;

        public EnqueueEmailRequest(
            IAzureTableService tableService,
            ITokenValidationService tokenValidationService,
            ILogger<EnqueueEmailRequest> logger,
            IGraphEmailService graphEmailService,
            IConfiguration configuration,
            IGraphClientService graphClientService
        )
        {
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));

            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));

            _graphEmailService =
                graphEmailService ?? throw new ArgumentNullException(nameof(graphEmailService));
            _logger = logger;
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
        }

        [Function("EnqueueEmailRequest")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to enqueue an email request at {Time}.",
                DateTime.UtcNow
            );

            try
            {
                // make sure we can connect to the queue otherwise no point in doing all this work

                // Create QueueClient
                string? connectionString = _configuration["AzureWebJobsStorage"];

                string queueName = "messageprocessqueue";
                var queueClient = new QueueClient(connectionString, queueName);
                // Ensure the queue exists (optional)
                var createResult = await queueClient.CreateIfNotExistsAsync();
                if (createResult != null)
                {
                    _logger.LogInformation("Queue {QueueName} created.", queueName);
                }
                else
                {
                    _logger.LogInformation("Queue {QueueName} already exists.", queueName);
                }

                string fboQueueName = "filedbyothersqueue";
                var fboQueueClient = new QueueClient(connectionString, fboQueueName);
                // Ensure the queue exists (optional)
                var createFBOResult = await fboQueueClient.CreateIfNotExistsAsync();
                if (createFBOResult != null)
                {
                    _logger.LogInformation("Queue {QueueName} created.", fboQueueName);
                }
                else
                {
                    _logger.LogInformation("Queue {QueueName} already exists.", fboQueueName);
                }

                string ffQueueName = "futurefilingqueue";
                var ffQueueClient = new QueueClient(connectionString, ffQueueName);
                // Ensure the queue exists (optional)
                var createFFResult = await ffQueueClient.CreateIfNotExistsAsync();
                if (createFFResult != null)
                {
                    _logger.LogInformation("Queue {QueueName} created.", ffQueueName);
                }
                else
                {
                    _logger.LogInformation("Queue {QueueName} already exists.", ffQueueName);
                }

                string fosQueueName = "fileonsendqueue";
                var fosQueueClient = new QueueClient(connectionString, fosQueueName);
                // Ensure the queue exists (optional)
                var createFoSResult = await fosQueueClient.CreateIfNotExistsAsync();
                if (createFoSResult != null)
                {
                    _logger.LogInformation("Queue {QueueName} created.", fosQueueName);
                }
                else
                {
                    _logger.LogInformation("Queue {QueueName} already exists.", fosQueueName);
                }

                // Read and log the request body
                string requestBody;
                using (var streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                    _logger.LogDebug("Request body: {RequestBody}", requestBody);
                }

                if (cancellationToken.IsCancellationRequested)
                {
                    return null;
                }

                var fileEmailRequest = JsonConvert.DeserializeObject<FileEmailRequest>(requestBody);
                if (fileEmailRequest == null)
                {
                    _logger.LogWarning("Deserialized request data is null.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid request data.",
                        _logger
                    );
                }

                if (fileEmailRequest.Emails == null || !(fileEmailRequest.Emails.Count > 0))
                {
                    _logger.LogWarning("No emails provided in the request.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "No emails provided in the request.",
                        _logger
                    );
                }

                // Authenticate and get token

                var token = await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(
                    req
                );
                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogWarning("Failed to get a valid token.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to get a valid token.",
                        _logger
                    );
                }
                var tokenDetails = _tokenValidationService.GetUserDetails(token);

                var graphClient = _graphClientService.GetUserGraphClient(
                    token,
                    tokenDetails.ClientId,
                    tokenDetails.ClientSecret
                );

                DateTime? tokenExpiry = _tokenValidationService.GetTokenExpiryDateTime(token);

                string customerDomainRaw = ExtractDomainFromEmailOrUpn(tokenDetails.Upn, _logger);
                string user = tokenDetails.Upn ?? "unknown_user";

                string activeUser = string.IsNullOrEmpty(fileEmailRequest.SharedMailbox)
                    ? user
                    : fileEmailRequest.SharedMailbox;

                _logger.LogInformation("**** Running as " + activeUser);

                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                string customerDomain = customerDomainRaw;

                string? mappedDomain = await DomainUtils.MapDomain(
                    customerDomainRaw,
                    _tableService
                );
                if (mappedDomain != null)
                {
                    _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                    customerDomain = mappedDomain;
                }

                // convert the message IDs to persistent IDs

                // Oulook sends 100 messages max so we are good for up to 1000

                List<string> InputIds = new List<string>();

                string idsforDebug = "";

                foreach (Email email in fileEmailRequest.Emails)
                {
                    if (email != null && email.ItemId != null)
                    {
                        InputIds.Add(email.ItemId);
                        idsforDebug = idsforDebug + email.ItemId + ", ";
                    }
                }

                Dictionary<string, string>? mappedIds = null;

                if (InputIds.Count > 0)
                {
                    try
                    {
                        var res = await _graphEmailService.MapFromRestIdToPersistentIDs(
                            InputIds,
                            graphClient,
                            activeUser
                        );
                        if (res.IsSuccess)
                        {
                            mappedIds = res.Value;
                        }
                    }
                    catch (Exception ex)
                    {
                        if (ex.Message.Contains("Invalid value for arg"))
                        {
                            _logger.LogInformation("Failed to map to rest id, trying entry id");
                            // Try again with entry id
                            var res = await _graphEmailService.MapFromEntryIdToPersistentIDs(
                                InputIds,
                                graphClient,
                                activeUser
                            );
                            if (res.IsSuccess)
                            {
                                mappedIds = res.Value;
                            }
                        }
                        else
                        {
                            _logger.LogInformation(ex, "Failed to map IDs from ", idsforDebug);

                            throw ex;
                        }
                    }
                }

                foreach (Email email in fileEmailRequest.Emails)
                {
                    if (fileEmailRequest.ProjectCode != null && email.ItemId != null)
                    {
                        string? messageId = null;

                        // convert if we have it
                        if (mappedIds != null && mappedIds.ContainsKey(email.ItemId))
                            messageId = mappedIds[email.ItemId];
                        Message message = new Message()
                        {
                            PartitionKey = activeUser,
                            RowKey = Guid.NewGuid().ToString(),
                            MessageId = email.ItemId,
                            ProjectCode = fileEmailRequest.ProjectCode,
                            SitePath = fileEmailRequest.SitePath,
                            CustomerDomain = customerDomainRaw,
                            Tag = fileEmailRequest.Tag,
                            Important = fileEmailRequest.Important,
                            Confidential = fileEmailRequest.Confidential,
                            DequeueCount = 0,
                            Status = JobStatus.Pending,
                        };

                        if (!string.IsNullOrEmpty(fileEmailRequest.SharedMailbox))
                        {
                            message.SharedMailbox = fileEmailRequest.SharedMailbox;
                        }

                        if (messageId != null)
                        {
                            message.PersistentMessageId = messageId;
                        }
                        else { }

                        await _tableService.AddEntityAsync(
                            "QueuedMessage",
                            message,
                            customerDomain
                        );
                    }
                }

                // add a message to the queue

                var queueMessage = new FileEmailQueueMessage
                {
                    Token = token,
                    UserId = tokenDetails.Upn ?? "unknown_user",
                    CustomerDomain = customerDomain,
                };

                if (!string.IsNullOrEmpty(fileEmailRequest.SharedMailbox))
                {
                    _logger.LogInformation(
                        "Add to Proceess Queued for a shared mailbox "
                            + fileEmailRequest.SharedMailbox
                    );
                    queueMessage.SharedMailbox = fileEmailRequest.SharedMailbox;
                }

                // Serialize the queue message and base64 encode it
                var messageString = JsonConvert.SerializeObject(queueMessage);
                var base64Message = Convert.ToBase64String(Encoding.UTF8.GetBytes(messageString));

                try
                {
                    await queueClient.SendMessageAsync(base64Message);
                    _logger.LogInformation("Message processing queue message enqueued.");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to enqueue message to {QueueName}.", queueName);
                }

                if (Environment.GetEnvironmentVariable("ENABLE_FILED_BY_OTHERS") == "true")
                {
                    var fboMessage = new FiledByOthersQueueMessage
                    {
                        Token = token,
                        UserId = tokenDetails.Upn ?? "unknown_user",
                        CustomerDomain = customerDomain,
                    };

                    if (!string.IsNullOrEmpty(fileEmailRequest.SharedMailbox))
                    {
                        _logger.LogInformation(
                            "Add to Filed By Others Queue for a shared mailbox "
                                + fileEmailRequest.SharedMailbox
                        );
                        fboMessage.SharedMailbox = fileEmailRequest.SharedMailbox;
                    }

                    // Serialize the queue message and base64 encode it
                    var fboMessageString = JsonConvert.SerializeObject(fboMessage);
                    var base64FBOMessage = Convert.ToBase64String(
                        Encoding.UTF8.GetBytes(fboMessageString)
                    );
                    try
                    {
                        await fboQueueClient.SendMessageAsync(base64FBOMessage);
                        _logger.LogInformation("FBO processing queue message enqueued.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Failed to enqueue message to {QueueName}.",
                            fboQueueName
                        );
                    }
                }

                if (Environment.GetEnvironmentVariable("ENABLE_FUTURE_FILING") == "true")
                {
                    var ffMessage = new FutureFilingQueueMessage
                    {
                        Token = token,
                        UserId = tokenDetails.Upn ?? "unknown_user",
                        CustomerDomain = customerDomain,
                    };

                    if (!string.IsNullOrEmpty(fileEmailRequest.SharedMailbox))
                    {
                        _logger.LogInformation(
                            "Add to Future Filing Queue for a shared mailbox "
                                + fileEmailRequest.SharedMailbox
                        );

                        ffMessage.SharedMailbox = fileEmailRequest.SharedMailbox;
                    }

                    // Serialize the queue message and base64 encode it
                    var ffMessageString = JsonConvert.SerializeObject(ffMessage);
                    var base64FFMessage = Convert.ToBase64String(
                        Encoding.UTF8.GetBytes(ffMessageString)
                    );
                    try
                    {
                        await ffQueueClient.SendMessageAsync(base64FFMessage);
                        _logger.LogInformation("Future filing processing queue message enqueued.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Failed to enqueue message to {QueueName}.",
                            fboQueueName
                        );
                    }
                }

                if (Environment.GetEnvironmentVariable("ENABLE_FILE_ON_SEND") == "true")
                {
                    FileOnSendQueueMessage fosMessage = new FileOnSendQueueMessage
                    {
                        Token = token,
                        UserId = tokenDetails.Upn ?? "unknown_user",
                        CustomerDomain = customerDomain,
                    };

                    if (!string.IsNullOrEmpty(fileEmailRequest.SharedMailbox))
                    {
                        _logger.LogInformation(
                            "Add to FileOnSend Queue for a shared mailbox "
                                + fileEmailRequest.SharedMailbox
                        );

                        fosMessage.SharedMailbox = fileEmailRequest.SharedMailbox;
                    }

                    // Serialize the queue message and base64 encode it
                    System.String? fosMessageString = JsonConvert.SerializeObject(fosMessage);
                    System.String? base64FOSMessage = Convert.ToBase64String(
                        Encoding.UTF8.GetBytes(fosMessageString)
                    );
                    try
                    {
                        await fosQueueClient.SendMessageAsync(base64FOSMessage);
                        _logger.LogInformation("File on send processing queue message enqueued.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Failed to enqueue message to {QueueName}.",
                            fosQueueName
                        );
                    }
                }

                if (cancellationToken.IsCancellationRequested)
                {
                    return null;
                }

                return await ApiResponseUtility.CreateSuccessResponse<string>(
                    cancellationToken,
                    req,
                    "OK",
                    "Request successfully enqueued."
                );
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to deserialize request body.");
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Invalid request body format.",
                    _logger
                );
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "Argument exception occurred.");
                if (cancellationToken.IsCancellationRequested)
                {
                    return null;
                }
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Invalid argument provided.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process email request.");
                if (cancellationToken.IsCancellationRequested)
                {
                    return null;
                }

                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "Failed to process email request.",
                    _logger
                );
            }
        }

        public static string ExtractDomainFromEmailOrUpn(string? emailOrUpn, ILogger logger)
        {
            if (string.IsNullOrWhiteSpace(emailOrUpn))
            {
                logger.LogError("Email or UPN cannot be null or empty.");
                throw new ArgumentException(
                    "Email or UPN cannot be null or empty.",
                    nameof(emailOrUpn)
                );
            }

            var parts = emailOrUpn.Split('@');
            if (parts.Length != 2)
            {
                logger.LogError("The provided email or UPN is not in a valid format.");
                throw new FormatException("The provided email or UPN is not in a valid format.");
            }

            var domain = parts[1].Trim();
            if (
                string.IsNullOrWhiteSpace(domain) || !domain.Contains('.', StringComparison.Ordinal)
            )
            {
                logger.LogError("The domain part of the email or UPN is invalid.");
                throw new FormatException("The domain part of the email or UPN is invalid.");
            }

            string sanitizedDomain = EmailUtils.SanitizeCustomerDomain(domain);
            logger.LogInformation("Sanitized domain: {SanitizedDomain}", sanitizedDomain);
            return sanitizedDomain;
        }
    }
}
