using System.Net;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.FilingStatusCheck;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Atvero.Mail
{
    public class CheckConversationStatus
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<EnqueueEmailRequest> _logger;
        private readonly IConfiguration _configuration;
        private readonly IAzureTableService _tableService;

        private readonly IFilingStatusCheckService _filingStatusCheckService;

        public CheckConversationStatus(
            ITokenValidationService tokenValidationService,
            IFilingStatusCheckService filingStatusCheckService,
            ILogger<EnqueueEmailRequest> logger,
            IConfiguration configuration,
            IAzureTableService tableService
        )
        {
            _filingStatusCheckService = filingStatusCheckService;
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));

            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        [Function("CheckConversationStatus")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to check a conversation status {Time}.",
                DateTime.UtcNow
            );

            try
            {
                // make sure we can connect to the queue otherwise no point in doing all this work

                // Create QueueClient
                string? connectionString = _configuration["AzureWebJobsStorage"];

                // Authenticate and get token

                var token = await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(
                    req
                );
                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogWarning("Failed to get a valid token.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to get a valid token.",
                        _logger
                    );
                }

                var tokenDetails = _tokenValidationService.GetUserDetails(token);
                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                string customerDomain = ExtractDomainFromEmailOrUpn(tokenDetails.Upn, _logger);
                string? mappedDomain = await DomainUtils.MapDomain(customerDomain, _tableService);

                if (mappedDomain != null)
                {
                    _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                    customerDomain = mappedDomain;
                }
                string user = tokenDetails.Upn ?? "unknown_user";

                string? sharedMailbox = req.Query["sharedmailbox"];
                string? conversationid = req.Query["conversationid"];

                if (conversationid == null)
                {
                    _logger.LogWarning("Deserialized request data is null.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid request data.",
                        _logger
                    );
                }

                string activeUser = string.IsNullOrEmpty(sharedMailbox) ? user : sharedMailbox;

                var conversationIsAlreadyFiledResult =
                    await _filingStatusCheckService.CheckConversationIsFiledAsync(
                        activeUser,
                        conversationid,
                        customerDomain
                    );

                bool conversationIsAlreadyFiled = conversationIsAlreadyFiledResult.IsSuccess
                    ? conversationIsAlreadyFiledResult.Value
                    : false;

                // we don't trigger any processing, as the message won't be in Sent Items
                // wait on the next run for it to be picked up

                var status = new { status = conversationIsAlreadyFiled };

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    status,
                    ""
                );
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "Argument exception occurred.");
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Invalid argument provided.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process sync request.");
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "Failed to process email request.",
                    _logger
                );
            }
        }

        public static string ExtractDomainFromEmailOrUpn(string emailOrUpn, ILogger logger)
        {
            if (string.IsNullOrWhiteSpace(emailOrUpn))
            {
                logger.LogError("Email or UPN cannot be null or empty.");
                throw new ArgumentException(
                    "Email or UPN cannot be null or empty.",
                    nameof(emailOrUpn)
                );
            }

            var parts = emailOrUpn.Split('@');
            if (parts.Length != 2)
            {
                logger.LogError("The provided email or UPN is not in a valid format.");
                throw new FormatException("The provided email or UPN is not in a valid format.");
            }

            var domain = parts[1].Trim();
            if (
                string.IsNullOrWhiteSpace(domain) || !domain.Contains('.', StringComparison.Ordinal)
            )
            {
                logger.LogError("The domain part of the email or UPN is invalid.");
                throw new FormatException("The domain part of the email or UPN is invalid.");
            }

            string sanitizedDomain = EmailUtils.SanitizeCustomerDomain(domain);
            logger.LogInformation("Sanitized domain: {SanitizedDomain}", sanitizedDomain);
            return sanitizedDomain;
        }
    }
}
