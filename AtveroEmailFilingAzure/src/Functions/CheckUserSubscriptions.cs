using System.Net;
using AtveroEmailFiling.Functions;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Atvero.Mail
{
    class MiniSubscription
    {
        public string? subscription { get; set; }
    }

    public class CheckUserSubscription
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<EnqueueEmailRequest> _logger;
        private readonly IConfiguration _configuration;

        private readonly IAzureTableService _tableService;
        private readonly IGraphApiClient _graphApiClient;
        private readonly IGraphClientService _graphClientService;

        public CheckUserSubscription(
            IAzureTableService tableService,
            ITokenValidationService tokenValidationService,
            ILogger<EnqueueEmailRequest> logger,
            IConfiguration configuration,
            IGraphApiClient graphApiClient,
            IGraphClientService graphClientService
        )
        {
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));

            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
        }

        [Function("CheckUserSubscription")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to check user subscriptions {Time}.",
                DateTime.UtcNow
            );

            try
            {
                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);

                if (null == token)
                {
                    SentrySdk.CaptureMessage("Failed to validate authorization token.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to validate authorization token.",
                        _logger
                    );
                }

                AtveroEmailFiling.Models.TokenDetails? tokenDetails =
                    _tokenValidationService.GetUserDetails(token);

                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                string? sharedMailbox = req.Query["sharedmailbox"];

                string customerDomainRaw = EmailUtils.ExtractDomainFromEmailOrUpn(
                    tokenDetails.Upn,
                    _logger
                );

                string customerDomain = customerDomainRaw;

                string? mappedDomain = await DomainUtils.MapDomain(
                    customerDomainRaw,
                    _tableService
                );
                if (mappedDomain != null)
                {
                    _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                    customerDomain = mappedDomain;
                }

                string escapedEmail = (tokenDetails.Upn ?? "").Replace("'", "''");

                string filter = $"PartitionKey eq '{escapedEmail}'";
                List<MiniSubscription> subs = new List<MiniSubscription>();

                try
                {
                    List<MailSubscription> subscriptions =
                        await _tableService.QueryEntitiesAsync<MailSubscription>(
                            "Subscriptions",
                            filter,
                            customerDomain
                        );

                    if (subscriptions.Count > 0)
                    {
                        foreach (MailSubscription sub in subscriptions)
                        {
                            MiniSubscription hub = new MiniSubscription()
                            {
                                subscription = sub.RowKey,
                            };

                            if (!string.IsNullOrEmpty(sharedMailbox)) // we want subscriptions for the shared mailbox
                            {
                                if (
                                    !string.IsNullOrEmpty(sub.SharedMailbox)
                                    && sharedMailbox == sub.SharedMailbox
                                )
                                {
                                    subs.Add(hub);
                                }
                            }
                            else
                            {
                                // we want subscriptions not tied to a shared mailbox
                                if (string.IsNullOrEmpty(sub.SharedMailbox))
                                {
                                    subs.Add(hub);
                                }
                            }
                        }
                    }
                    return await ApiResponseUtility.CreateSuccessResponse(
                        cancellationToken,
                        req,
                        subs,
                        ""
                    );
                }
                catch (Exception ex)
                {
                    // almost certainly table doesn't exist
                    _logger.LogError(ex, "Failed checking user subscription");
                    _logger.LogInformation("Assuming failure is table doesn't exist");

                    return await ApiResponseUtility.CreateSuccessResponse(
                        cancellationToken,
                        req,
                        subs,
                        ""
                    );
                }
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
