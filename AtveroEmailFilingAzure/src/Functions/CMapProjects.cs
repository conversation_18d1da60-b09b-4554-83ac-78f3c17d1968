using System.Net;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.CMapService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace AtveroEmailFiling.Functions
{
    public class CMapProjects
    {
        private readonly ITokenValidationService _tokenValidationService;

        private readonly ILogger<CMapProjects> _logger;
        private readonly IConfiguration _config;
        private readonly IGraphClientService _graphClientService;

        private readonly IGraphApiClient _graphApiClient;

        public CMapProjects(
            ITokenValidationService tokenValidationService,
            IConfiguration config,
            IGraphClientService graphClientService,
            IGraphApiClient graphApiClient,
            ILogger<CMapProjects> logger
        )
        {
            _tokenValidationService = tokenValidationService;
            _graphApiClient = graphApiClient;
            _graphClientService = graphClientService;
            _logger = logger;
            _config = config;
        }

        [Function("CmapProjects")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            string? search = req.Query["search"];

            try
            {
                _logger.LogInformation("Attempting to authenticate and retrieve token.");

                // can't see a way to make this multi-geo right now

                Result<string> tenant = await FunctionUtils.GetTenantAsync(
                    req,
                    _logger,
                    _tokenValidationService,
                    _graphApiClient,
                    _graphClientService
                );

                if (tenant.IsFailed)
                {
                    IEnumerable<Exception> ex = ApiResponseUtility.ExtractExceptionsFromResult(
                        tenant.ToResult()
                    );

                    return await ApiResponseUtility.HandleInternalError(
                        cancellationToken,
                        req,
                        ex,
                        "Unable to get SharePoint tenant.",
                        _logger
                    );
                }

                Result<CMapAuthService> cMapAuthService = await CMapAuthService.FactoryAsync(
                    new HttpClient(), // Override your httpclient behaviour here
                    _config,
                    tenant.Value,
                    _logger
                );

                if (cMapAuthService.IsFailed)
                {
                    _logger.LogInformation("Failed to retrieve token.");

                    IEnumerable<Exception> ex = ApiResponseUtility.ExtractExceptionsFromResult(
                        cMapAuthService.ToResult()
                    );
                    return await ApiResponseUtility.HandleInternalError(
                        cancellationToken,
                        req,
                        ex,
                        "Could not initialise the CMap",
                        _logger
                    );

                    // whilst this is called all the time, don't raise these as errors

                    // HttpResponseData okresponse = req.CreateResponse(HttpStatusCode.OK);
                    // // Step 4: Write the response with project list
                    // await okresponse.WriteAsJsonAsync(new List<Project>());

                    // // No need to set StatusCode after writing the response
                    // return okresponse; // Return after writing the content
                }

                if (cMapAuthService.Value == null)
                {
                    _logger.LogError("CMapAuthService is null.");

                    return req.CreateResponse(HttpStatusCode.InternalServerError);
                }

                Result<List<Models.CmapProjects.Project>> projects = await GetCMapProjects(
                    cMapAuthService,
                    search
                );

                if (cMapAuthService.IsFailed)
                {
                    IEnumerable<Exception> ex = ApiResponseUtility.ExtractExceptionsFromResult(
                        projects.ToResult()
                    );
                    return await ApiResponseUtility.HandleInternalError(
                        cancellationToken,
                        req,
                        ex,
                        "Could not get projects from CMap",
                        _logger
                    );
                }
                HttpResponseData response = req.CreateResponse(HttpStatusCode.OK);
                // Step 4: Write the response with project list
                await response.WriteAsJsonAsync(projects.Value);

                // No need to set StatusCode after writing the response
                return response; // Return after writing the content
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, $"HttpRequestException occurred");

                return req.CreateResponse(HttpStatusCode.BadRequest); // Return after catching the error
            }
            catch (System.Exception ex)
            {
                _logger.LogError(ex, $"An unexpected error occurred");

                return req.CreateResponse(HttpStatusCode.InternalServerError); // Return after catching the error
            }
        }

        public async Task<
            Result<List<AtveroEmailFiling.Models.CmapProjects.Project>>
        > GetCMapProjects(Result<CMapAuthService> cMapAuthService, string? search)
        {
            if (null == cMapAuthService.Value?.ApiKey)
            {
                return Result.Fail("CMapAuthService is null or ApiKey is null");
            }
            CMapService _cmapService = new CMapService(cMapAuthService.Value.ApiKey, _logger);

            Result<HttpClient> cmapClient = await cMapAuthService.Value.GetClientAsync();
            if (cmapClient.IsFailed)
            {
                return cmapClient.ToResult();
            }

            _logger.LogInformation("Token retrieved successfully. Fetching projects...");

            // Step 3: Get Projects from CMapService using the token
            Result<List<AtveroEmailFiling.Models.CmapProjects.Project>> projects =
                await _cmapService.GetProjectsAsync(cmapClient.Value, search);
            return projects;
        }
    }
}
