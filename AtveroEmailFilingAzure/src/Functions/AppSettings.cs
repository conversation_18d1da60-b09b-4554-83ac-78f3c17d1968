using System.Net;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;

namespace Atvero.Mail
{
    public class AppSettings
    {
        private readonly ILogger<EnqueueEmailRequest> _logger;

        private readonly IAzureTableService _tableService;

        private readonly ITokenValidationService _tokenValidationService;

        public AppSettings(
            IAzureTableService tableService,
            ILogger<EnqueueEmailRequest> logger,
            ITokenValidationService tokenValidationService
        )
        {
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
        }

        [Function("AppSettings")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation("Received a request to get app settings.", DateTime.UtcNow);

            try
            {
                // look up setting request

                string? settingName = req.Query["setting"];
                string? customerDomain = req.Query["domain"];

                // special case, useNAA we call without a token as we are pre-authentication
                // everything else we extract the domain

                if (customerDomain == null)
                {
                    // must extract it from the token

                    System.String? token =
                        await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(req);
                    if (string.IsNullOrEmpty(token))
                    {
                        _logger.LogWarning("Failed to get a valid token.");
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.Unauthorized,
                            "Failed to get a valid token.",
                            _logger
                        );
                    }

                    AtveroEmailFiling.Models.TokenDetails? tokenDetails =
                        _tokenValidationService.GetUserDetails(token);

                    customerDomain = EmailUtils.ExtractDomainFromEmailOrUpn(
                        tokenDetails.Upn,
                        _logger
                    );
                    string? mappedDomain = await DomainUtils.MapDomain(
                        customerDomain,
                        _tableService
                    );
                    if (mappedDomain != null)
                    {
                        _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                        customerDomain = mappedDomain;
                    }
                }

                _logger.LogInformation(
                    "Getting settings for " + settingName + " in domain " + customerDomain
                );

                if (settingName != null && customerDomain != null)
                {
                    // get setting from table for domain

                    FluentResults.Result<AppSetting>? settingRes =
                        await _tableService.GetEntityAsync<AppSetting>(
                            "AppSettings",
                            customerDomain,
                            settingName,
                            ""
                        );

                    if (settingRes.IsSuccess)
                    {
                        return await ApiResponseUtility.CreateSuccessResponse<string>(
                            cancellationToken,
                            req,
                            "OK",
                            settingRes.Value.SettingValue ?? ""
                        );
                    }
                }

                return await ApiResponseUtility.CreateSuccessResponse<string>(
                    cancellationToken,
                    req,
                    "OK",
                    ""
                );
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "Argument exception occurred.");
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Invalid argument provided.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process sync request.");
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "Failed to process app setttings request.",
                    _logger
                );
            }
        }
    }
}
