using System.Net;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Utils;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class CreateWeeklyUserStats
    {
        private readonly ILogger<CreateWeeklyUserStats> _logger;
        private readonly IAzureTableService _tableService;
        private readonly IConfiguration _configuration;

        public CreateWeeklyUserStats(
            ILogger<CreateWeeklyUserStats> logger,
            IAzureTableService tableService,
            IConfiguration configuration
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        [Function("CreateWeeklyUserStats")]
        public async Task<HttpResponseData> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req
        )
        {
            _logger.LogInformation(
                "CreateWeeklyUserStats function triggered at {Time}",
                DateTime.UtcNow
            );

            try
            {
                // Validate shared API key
                if (
                    !FunctionUtils.ValidateApiKey(
                        req,
                        _configuration,
                        _logger,
                        "CreateWeeklyUserStats"
                    )
                )
                {
                    _logger.LogWarning(
                        "CreateWeeklyUserStats access denied - invalid or missing API key"
                    );
                    var errorResponse = req.CreateResponse(HttpStatusCode.Unauthorized);
                    await errorResponse.WriteStringAsync(
                        JsonConvert.SerializeObject(
                            new { success = false, message = "Unauthorized - Invalid API key" }
                        )
                    );
                    return errorResponse;
                }

                // Process weekly stats
                var userResult = await ProcessUserFilingStats();
                var domainResult = await ProcessDomainFilingStats();
                var clickResult = await ProcessUserClickFilingStats();
                var autoResult = await ProcessAutoFilingStats();
                var projectResult = await ProcessProjectFilingStats();

                var response = req.CreateResponse(HttpStatusCode.OK);
                await response.WriteStringAsync(
                    JsonConvert.SerializeObject(
                        new
                        {
                            success = true,
                            message = "Weekly user, domain, click, auto, and project filing stats creation completed successfully",
                            weeklyStats = new
                            {
                                userStats = new
                                {
                                    processed = userResult.processed,
                                    created = userResult.created,
                                },
                                domainStats = new
                                {
                                    processed = domainResult.processed,
                                    created = domainResult.created,
                                },
                                clickStats = new
                                {
                                    processed = clickResult.processed,
                                    created = clickResult.created,
                                },
                                autoStats = new
                                {
                                    processed = autoResult.processed,
                                    created = autoResult.created,
                                },
                                projectStats = new
                                {
                                    processed = projectResult.processed,
                                    created = projectResult.created,
                                },
                                totalProcessed = userResult.processed
                                    + domainResult.processed
                                    + clickResult.processed
                                    + autoResult.processed
                                    + projectResult.processed,
                                totalCreated = userResult.created
                                    + domainResult.created
                                    + clickResult.created
                                    + autoResult.created
                                    + projectResult.created,
                                weekPrefix = GetCurrentWeekPrefix(),
                            },
                        }
                    )
                );

                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while creating weekly user stats");

                var errorResponse = req.CreateResponse(HttpStatusCode.InternalServerError);
                await errorResponse.WriteStringAsync(
                    JsonConvert.SerializeObject(
                        new
                        {
                            success = false,
                            message = "Error occurred while creating weekly user stats",
                            error = ex.Message,
                        }
                    )
                );

                return errorResponse;
            }
        }

        private async Task<(int processed, int created)> ProcessUserFilingStats()
        {
            try
            {
                _logger.LogInformation("Processing User Filing stats");

                // Query all entries from AAUserFilingStats
                List<UserStat> userStats = await _tableService.QueryEntitiesAsync<UserStat>(
                    "AAUserFilingStats",
                    "", // Empty filter to get all entries
                    ""
                );

                _logger.LogInformation("Found {Count} user filing stats", userStats.Count);

                int created = 0;
                string weekPrefix = GetCurrentWeekPrefix();

                foreach (UserStat userStat in userStats)
                {
                    string partitionKey = $"{weekPrefix}_{userStat.PartitionKey}";
                    string rowKey = userStat.RowKey;

                    // Check if weekly stats entry already exists
                    var existingStatsResult = await _tableService.GetEntityAsync<WeeklyUserStats>(
                        "AAWeeklyUserStats",
                        partitionKey,
                        rowKey,
                        ""
                    );

                    WeeklyUserStats weeklyStats;
                    if (existingStatsResult.IsSuccess)
                    {
                        // Update existing entry by adding current FiledCount
                        weeklyStats = existingStatsResult.Value;
                        weeklyStats.WeeklyCount += userStat.FiledCount;
                    }
                    else
                    {
                        // Create new WeeklyUserStats entry
                        weeklyStats = new WeeklyUserStats
                        {
                            PartitionKey = partitionKey,
                            RowKey = rowKey,
                            WeeklyCount = userStat.FiledCount,
                        };
                    }

                    // Upsert the weekly stats entry
                    await _tableService.UpsertEntityAsync("AAWeeklyUserStats", weeklyStats, "");

                    // Update original entry: add FiledCount to AllTimeCount, then reset FiledCount
                    userStat.AllTimeCount += userStat.FiledCount;

                    userStat.FiledCount = 0;
                    await _tableService.UpdateEntityAsync("AAUserFilingStats", userStat, "");

                    created++;
                }

                _logger.LogInformation("Created {Created} weekly user stats entries", created);

                return (userStats.Count, created);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing AAUserFilingStats");
                throw;
            }
        }

        private async Task<(int processed, int created)> ProcessDomainFilingStats()
        {
            try
            {
                _logger.LogInformation("Processing Domain Filing stats");

                // Query all entries from AADomainFilingStats
                List<UserStat> domainStats = await _tableService.QueryEntitiesAsync<UserStat>(
                    "AADomainFilingStats",
                    "", // Empty filter to get all entries
                    ""
                );

                _logger.LogInformation("Found {Count} domain filing stats", domainStats.Count);

                int created = 0;
                string weekPrefix = GetCurrentWeekPrefix();

                foreach (UserStat domainStat in domainStats)
                {
                    string partitionKey = weekPrefix; // Just YYYY-WW format
                    string rowKey = domainStat.PartitionKey; // Customer domain from original partition key

                    // Check if weekly stats entry already exists
                    var existingStatsResult = await _tableService.GetEntityAsync<WeeklyDomainStats>(
                        "AAWeeklyDomainStats",
                        partitionKey,
                        rowKey,
                        ""
                    );

                    WeeklyDomainStats weeklyStats;
                    if (existingStatsResult.IsSuccess)
                    {
                        // Update existing entry by adding current FiledCount
                        weeklyStats = existingStatsResult.Value;
                        weeklyStats.WeeklyCount += domainStat.FiledCount;
                    }
                    else
                    {
                        // Create new WeeklyDomainStats entry
                        weeklyStats = new WeeklyDomainStats
                        {
                            PartitionKey = partitionKey,
                            RowKey = rowKey,
                            WeeklyCount = domainStat.FiledCount,
                        };
                    }

                    // Upsert the weekly stats entry
                    await _tableService.UpsertEntityAsync("AAWeeklyDomainStats", weeklyStats, "");

                    // Update original entry: add FiledCount to AllTimeCount, then reset FiledCount
                    domainStat.AllTimeCount += domainStat.FiledCount;
                    domainStat.FiledCount = 0;
                    await _tableService.UpdateEntityAsync("AADomainFilingStats", domainStat, "");

                    created++;
                }

                _logger.LogInformation("Created {Created} weekly domain stats entries", created);

                return (domainStats.Count, created);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing AADomainFilingStats");
                throw;
            }
        }

        private async Task<(int processed, int created)> ProcessUserClickFilingStats()
        {
            try
            {
                _logger.LogInformation("Processing User Click Filing stats");

                // Query all entries from AAActiveClickUserStats
                List<UserStat> clickStats = await _tableService.QueryEntitiesAsync<UserStat>(
                    "AAActiveClickUserStats",
                    "", // Empty filter to get all entries
                    ""
                );

                _logger.LogInformation("Found {Count} user click filing stats", clickStats.Count);

                int created = 0;
                string weekPrefix = GetCurrentWeekPrefix();

                foreach (UserStat clickStat in clickStats)
                {
                    string partitionKey = $"{weekPrefix}_{clickStat.PartitionKey}";
                    string rowKey = clickStat.RowKey;

                    // Check if weekly stats entry already exists
                    var existingStatsResult =
                        await _tableService.GetEntityAsync<WeeklyUserClickStats>(
                            "AAWeeklyUserClickStats",
                            partitionKey,
                            rowKey,
                            ""
                        );

                    WeeklyUserClickStats weeklyStats;
                    if (existingStatsResult.IsSuccess)
                    {
                        // Update existing entry by adding current FiledCount
                        weeklyStats = existingStatsResult.Value;
                        weeklyStats.WeeklyCount += clickStat.FiledCount;
                    }
                    else
                    {
                        // Create new WeeklyUserClickStats entry
                        weeklyStats = new WeeklyUserClickStats
                        {
                            PartitionKey = partitionKey,
                            RowKey = rowKey,
                            WeeklyCount = clickStat.FiledCount,
                        };
                    }

                    // Upsert the weekly stats entry
                    await _tableService.UpsertEntityAsync(
                        "AAWeeklyUserClickStats",
                        weeklyStats,
                        ""
                    );

                    // Update original entry: add FiledCount to AllTimeCount, then reset FiledCount
                    clickStat.AllTimeCount += clickStat.FiledCount;
                    clickStat.FiledCount = 0;
                    await _tableService.UpdateEntityAsync("AAActiveClickUserStats", clickStat, "");

                    created++;
                }

                _logger.LogInformation(
                    "Created {Created} weekly user click stats entries",
                    created
                );

                return (clickStats.Count, created);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing AAActiveClickUserStats");
                throw;
            }
        }

        private async Task<(int processed, int created)> ProcessAutoFilingStats()
        {
            try
            {
                _logger.LogInformation("Processing Auto Filing stats");

                // Query all entries from AAAutoFilingStats
                List<UserStat> autoStats = await _tableService.QueryEntitiesAsync<UserStat>(
                    "AAAutoFilingStats",
                    "", // Empty filter to get all entries
                    ""
                );

                _logger.LogInformation("Found {Count} auto filing stats", autoStats.Count);

                int created = 0;
                string weekPrefix = GetCurrentWeekPrefix();

                foreach (UserStat autoStat in autoStats)
                {
                    string partitionKey = $"{weekPrefix}_{autoStat.PartitionKey}";
                    string rowKey = autoStat.RowKey;

                    // Check if weekly stats entry already exists
                    var existingStatsResult =
                        await _tableService.GetEntityAsync<WeeklyAutoFilingStats>(
                            "AAWeeklyAutoFilingStats",
                            partitionKey,
                            rowKey,
                            ""
                        );

                    WeeklyAutoFilingStats weeklyStats;
                    if (existingStatsResult.IsSuccess)
                    {
                        // Update existing entry by adding current FiledCount
                        weeklyStats = existingStatsResult.Value;
                        weeklyStats.WeeklyCount += autoStat.FiledCount;
                    }
                    else
                    {
                        // Create new WeeklyAutoFilingStats entry
                        weeklyStats = new WeeklyAutoFilingStats
                        {
                            PartitionKey = partitionKey,
                            RowKey = rowKey,
                            WeeklyCount = autoStat.FiledCount,
                        };
                    }

                    // Upsert the weekly stats entry
                    await _tableService.UpsertEntityAsync(
                        "AAWeeklyAutoFilingStats",
                        weeklyStats,
                        ""
                    );

                    // Update original entry: add FiledCount to AllTimeCount, then reset FiledCount
                    autoStat.AllTimeCount += autoStat.FiledCount;
                    autoStat.FiledCount = 0;
                    await _tableService.UpdateEntityAsync("AAAutoFilingStats", autoStat, "");

                    created++;
                }

                _logger.LogInformation(
                    "Created {Created} weekly auto filing stats entries",
                    created
                );

                return (autoStats.Count, created);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing AAAutoFilingStats");
                throw;
            }
        }

        private async Task<(int processed, int created)> ProcessProjectFilingStats()
        {
            try
            {
                _logger.LogInformation("Processing Project Filing stats");

                // Query all entries from AAProjectCustomerStats
                List<UserStat> projectStats = await _tableService.QueryEntitiesAsync<UserStat>(
                    "AAProjectCustomerStats",
                    "", // Empty filter to get all entries
                    ""
                );

                _logger.LogInformation("Found {Count} project filing stats", projectStats.Count);

                int created = 0;
                string weekPrefix = GetCurrentWeekPrefix();

                foreach (UserStat projectStat in projectStats)
                {
                    string partitionKey = $"{weekPrefix}_{projectStat.PartitionKey}";
                    string rowKey = projectStat.RowKey;

                    // Check if weekly stats entry already exists
                    var existingStatsResult =
                        await _tableService.GetEntityAsync<WeeklyProjectStats>(
                            "AAWeeklyProjectStats",
                            partitionKey,
                            rowKey,
                            ""
                        );

                    WeeklyProjectStats weeklyStats;
                    if (existingStatsResult.IsSuccess)
                    {
                        // Update existing entry by adding current FiledCount
                        weeklyStats = existingStatsResult.Value;
                        weeklyStats.WeeklyCount += projectStat.FiledCount;
                    }
                    else
                    {
                        // Create new WeeklyProjectStats entry
                        weeklyStats = new WeeklyProjectStats
                        {
                            PartitionKey = partitionKey,
                            RowKey = rowKey,
                            WeeklyCount = projectStat.FiledCount,
                        };
                    }

                    // Upsert the weekly stats entry
                    await _tableService.UpsertEntityAsync("AAWeeklyProjectStats", weeklyStats, "");

                    // Update original entry: add FiledCount to AllTimeCount and MonthlyCount, then reset FiledCount
                    projectStat.AllTimeCount += projectStat.FiledCount;
                    projectStat.MonthlyFiledCount += projectStat.FiledCount;
                    projectStat.FiledCount = 0;
                    await _tableService.UpdateEntityAsync(
                        "AAProjectCustomerStats",
                        projectStat,
                        ""
                    );

                    created++;
                }

                _logger.LogInformation("Created {Created} weekly project stats entries", created);

                return (projectStats.Count, created);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing AAProjectCustomerStats");
                throw;
            }
        }

        private static string GetCurrentWeekPrefix()
        {
            DateTime now = DateTime.UtcNow;
            int weekNumber = GetIso8601WeekOfYear(now);
            return $"{now.Year}-{weekNumber:D2}";
        }

        // Helper method to get ISO 8601 week number (copied from ProcessWeeklyStats.cs)
        private static int GetIso8601WeekOfYear(DateTime date)
        {
            // Return the ISO 8601 week of year
            var day = (int)
                System.Globalization.CultureInfo.InvariantCulture.Calendar.GetDayOfWeek(date);
            if (day == 0)
                day = 7; // Sunday is 7, not 0

            // Add days to get to Thursday (ISO 8601 week is based on Thursday)
            date = date.AddDays(4 - day);

            // Get the first day of the year
            var startOfYear = new DateTime(date.Year, 1, 1);

            // Calculate the number of days from the start of the year
            var days = (date - startOfYear).TotalDays;

            // Return the week number
            return (int)(days / 7) + 1;
        }
    }
}
