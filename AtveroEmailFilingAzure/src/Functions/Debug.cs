using System.Net;
using System.Text;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Models.QueueMessages;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using AtveroEmailFilingAzure.Services;
using Azure.Storage.Queues;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Atvero.Mail
{
    public class Debug
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<EnqueueEmailRequest> _logger;
        private readonly IConfiguration _configuration;

        private readonly IAzureTableService _tableService;

        public Debug(
            IAzureTableService tableService,
            ITokenValidationService tokenValidationService,
            ILogger<EnqueueEmailRequest> logger,
            IConfiguration configuration
        )
        {
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));

            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        [Function("Debug")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to enqueue an email request at {Time}.",
                DateTime.UtcNow
            );

            try
            {
                // make sure we can connect to the queue otherwise no point in doing all this work

                // Create QueueClient
                string? connectionString = _configuration["AzureWebJobsStorage"];

                string queueName = "messageprocessqueue";
                var queueClient = new QueueClient(connectionString, queueName);
                // Ensure the queue exists (optional)
                var createResult = await queueClient.CreateIfNotExistsAsync();
                if (createResult != null)
                {
                    _logger.LogInformation("Queue {QueueName} created.", queueName);
                }
                else
                {
                    _logger.LogInformation("Queue {QueueName} already exists.", queueName);
                }

                // string fboQueueName = "filedbyothersqueue";
                // var fboQueueClient = new QueueClient(connectionString, fboQueueName);
                // // Ensure the queue exists (optional)
                // var createFBOResult = await fboQueueClient.CreateIfNotExistsAsync();
                // if (createFBOResult != null)
                // {
                //     _logger.LogInformation("Queue {QueueName} created.", fboQueueName);
                // }
                // else
                // {
                //     _logger.LogInformation("Queue {QueueName} already exists.", fboQueueName);
                // }

                // string ffQueueName = "futurefilingqueue";
                // var ffQueueClient = new QueueClient(connectionString, ffQueueName);
                // // Ensure the queue exists (optional)
                // var createFFResult = await ffQueueClient.CreateIfNotExistsAsync();
                // if (createFFResult != null)
                // {
                //     _logger.LogInformation("Queue {QueueName} created.", ffQueueName);
                // }
                // else
                // {
                //     _logger.LogInformation("Queue {QueueName} already exists.", ffQueueName);
                // }

                // Read and log the request body
                string requestBody;
                using (var streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                    // _logger.LogDebug("Request body: {RequestBody}", requestBody);
                }

                var fileEmailRequest = JsonConvert.DeserializeObject<FileEmailRequest>(requestBody);
                if (fileEmailRequest == null)
                {
                    _logger.LogWarning("Deserialized request data is null.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid request data.",
                        _logger
                    );
                }

                // if (fileEmailRequest.Emails == null || !(fileEmailRequest.Emails.Count > 0))
                // {
                //     _logger.LogWarning("No emails provided in the request.");
                //     return await ApiResponseUtility.CreateErrorResponse<string>(
                //         req.CreateResponse(),
                //         HttpStatusCode.BadRequest,
                //         "No emails provided in the request.",
                //         _logger
                //     );
                // }

                // Authenticate and get token

                var token = await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(
                    req
                );
                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogWarning("Failed to get a valid token.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to get a valid token.",
                        _logger
                    );
                }

                DateTime? tokenExpiry = _tokenValidationService.GetTokenExpiryDateTime(token);
                var tokenDetails = _tokenValidationService.GetUserDetails(token);

                string customerDomainRaw = ExtractDomainFromEmailOrUpn(tokenDetails.Upn, _logger);
                string user = tokenDetails.Upn ?? "unknown_user";

                string customerDomain = customerDomainRaw;

                string? mappedDomain = await DomainUtils.MapDomain(
                    customerDomainRaw,
                    _tableService
                );
                if (mappedDomain != null)
                {
                    _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                    customerDomain = mappedDomain;
                }

                var queueMessage = new FileEmailQueueMessage
                {
                    Token = token,
                    UserId = tokenDetails.Upn ?? "unknown_user",
                    CustomerDomain = customerDomain,
                };

                // Serialize the queue message and base64 encode it
                var messageString = JsonConvert.SerializeObject(queueMessage);
                var base64Message = Convert.ToBase64String(Encoding.UTF8.GetBytes(messageString));

                try
                {
                    await queueClient.SendMessageAsync(base64Message);
                    _logger.LogInformation("Message processing queue message enqueued.");
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Failed to enqueue message to {QueueName}.", queueName);
                }

                return await ApiResponseUtility.CreateSuccessResponse<string>(
                    cancellationToken,
                    req,
                    "OK",
                    "Request successfully processed."
                );
            }
            catch (JsonException ex)
            {
                _logger.LogError(ex, "Failed to deserialize request body.");
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Invalid request body format.",
                    _logger
                );
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "Argument exception occurred.");
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Invalid argument provided.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process email request.");
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "Failed to process email request.",
                    _logger
                );
            }
        }

        public static string ExtractDomainFromEmailOrUpn(string? emailOrUpn, ILogger logger)
        {
            if (string.IsNullOrWhiteSpace(emailOrUpn))
            {
                logger.LogError("Email or UPN cannot be null or empty.");
                throw new ArgumentException(
                    "Email or UPN cannot be null or empty.",
                    nameof(emailOrUpn)
                );
            }

            var parts = emailOrUpn.Split('@');
            if (parts.Length != 2)
            {
                logger.LogError("The provided email or UPN is not in a valid format.");
                throw new FormatException("The provided email or UPN is not in a valid format.");
            }

            var domain = parts[1].Trim();
            if (
                string.IsNullOrWhiteSpace(domain) || !domain.Contains('.', StringComparison.Ordinal)
            )
            {
                logger.LogError("The domain part of the email or UPN is invalid.");
                throw new FormatException("The domain part of the email or UPN is invalid.");
            }

            string sanitizedDomain = EmailUtils.SanitizeCustomerDomain(domain);
            logger.LogInformation("Sanitized domain: {SanitizedDomain}", sanitizedDomain);
            return sanitizedDomain;
        }
    }
}
