using System.Net;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using AtveroEmailFilingAzure.src.Services.SharepointApi;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    class MiniHub
    {
        public required string Name { get; set; }
        public required string SiteUrl { get; set; }
        public required string DisplayName { get; set; }

        public required bool CustomTags { get; set; }
    }

    public class Hubsites
    {
        private readonly ILogger<Hubsites> _logger;
        private readonly IGraphClientService _graphClientService;
        private readonly ITokenValidationService _tokenValidationService;

        private readonly IGraphApiClient _graphApiClient;

        private readonly ISharepointApiFactory _sharepointApiFactory;

        private readonly IAzureTableService _tableService;

        public Hubsites(
            ILogger<Hubsites> logger,
            ITokenValidationService tokenValidationService,
            IGraphClientService graphClientService,
            IGraphApiClient graphApiClient,
            ISharepointApiFactory sharepointApiFactory,
            IAzureTableService tableService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));

            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _sharepointApiFactory =
                sharepointApiFactory
                ?? throw new ArgumentNullException(nameof(_sharepointApiFactory));
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));
        }

        [Function("Hubsites")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            try
            {
                // can't be made multi-geo
                // left in as fall back, as we should get hubsite list from az table anyway

                Result<string> tenant = await FunctionUtils.GetTenantAsync(
                    req,
                    _logger,
                    _tokenValidationService,
                    _graphApiClient,
                    _graphClientService
                );
                if (tenant.IsFailed)
                {
                    IEnumerable<Exception> ex = ApiResponseUtility.ExtractExceptionsFromResult(
                        tenant.ToResult()
                    );

                    return await ApiResponseUtility.HandleBadRequest(
                        cancellationToken,
                        req,
                        ex,
                        "Unable to get SharePoint tenant.",
                        _logger
                    );
                }

                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);

                if (null == token)
                {
                    SentrySdk.CaptureMessage("Failed to validate authorization token.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to validate authorization token.",
                        _logger
                    );
                }

                var tokenDetails = _tokenValidationService.GetUserDetails(token);

                if (tokenDetails.ClientId == null || tokenDetails.ClientSecret == null)
                {
                    SentrySdk.CaptureMessage("Failed to get client id and secret from token.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to get client id and secret from token.",
                        _logger
                    );
                }

                if (_tokenValidationService.IsTokenExpired(token))
                {
                    SentrySdk.CaptureMessage("Token is expired.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Token is expired.",
                        _logger
                    );
                }

                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                SharepointApi? sharepointApi = await _sharepointApiFactory.CreateSharepointApiAsync(
                    req,
                    tenant.Value,
                    token,
                    tokenDetails.ClientId,
                    tokenDetails.ClientSecret,
                    _logger
                );

                if (null == sharepointApi)
                {
                    SentrySdk.CaptureMessage("Failed to create SharePoint API.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Failed to create SharePoint API.",
                        _logger
                    );
                }

                // First try and get a list of the hubsites from an Azure table

                bool hubsitesFromTable = false;

                List<MiniHub> hubsites = new List<MiniHub>();

                try
                {
                    string customerDomainRaw = EmailUtils.ExtractDomainFromEmailOrUpn(
                        tokenDetails.Upn,
                        _logger
                    );

                    string customerDomain = customerDomainRaw;

                    string? mappedDomain = await DomainUtils.MapDomain(
                        customerDomainRaw,
                        _tableService
                    );
                    if (mappedDomain != null)
                    {
                        _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                        customerDomain = mappedDomain;
                    }

                    string filter = $"PartitionKey eq '{customerDomain}'";
                    List<AtveroEmailFiling.Models.AzureTables.HubSite> HubSiteEntries =
                        await _tableService.QueryEntitiesAsync<HubSite>(
                            "HubSites",
                            filter,
                            "" // global table
                        );

                    if (HubSiteEntries.Count > 0)
                    {
                        foreach (HubSite site in HubSiteEntries)
                        {
                            Result<SharePointSite> hubsite = await sharepointApi.GetSite(
                                site.SiteUrl
                            );
                            if (hubsite.IsSuccess)
                            {
                                Result<string> name = SharepointUtils.GetSiteNameFromUrl(
                                    hubsite.Value.Url
                                );
                                MiniHub hub = new MiniHub()
                                {
                                    Name = name.IsSuccess ? name.Value : hubsite.Value.Title,
                                    SiteUrl = hubsite.Value.Url,
                                    DisplayName = hubsite.Value.Title,
                                    CustomTags = false,
                                };

                                if (site.CustomTags)
                                {
                                    hub.CustomTags = site.CustomTags;
                                }

                                hubsites.Add(hub);
                            }
                        }

                        hubsitesFromTable = true;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogInformation("Not able to get hubsites from table");
                    _logger.LogInformation(ex.Message);
                }

                if (!hubsitesFromTable)
                {
                    try
                    {
                        Result<
                            List<AtveroEmailFilingAzure.src.Models.ApiResponses.Hubsite>
                        > hubsitesRes = await sharepointApi.GetAtveroMailHubsites();

                        if (hubsitesRes.IsSuccess)
                        {
                            foreach (
                                AtveroEmailFilingAzure.src.Models.ApiResponses.Hubsite site in hubsitesRes.Value
                            )
                            {
                                MiniHub hub = new MiniHub()
                                {
                                    Name = site.Name,
                                    SiteUrl = site.SiteUrl,
                                    DisplayName = site.Title,
                                    CustomTags = false,
                                };

                                hubsites.Add(hub);
                            }
                        }
                        else
                        {
                            IEnumerable<Exception> ex =
                                ApiResponseUtility.ExtractExceptionsFromResult(
                                    hubsitesRes.ToResult()
                                );

                            SentrySdk.CaptureMessage("Failed to get Atvero Mail Hubsites");

                            return await ApiResponseUtility.HandleInternalError(
                                cancellationToken,
                                req,
                                ex,
                                "Could not get hubsites",
                                _logger
                            );
                        }
                    }
                    catch (InvalidOperationException ex)
                    {
                        return await ApiResponseUtility.HandleInternalError(
                            cancellationToken,
                            req,
                            ex,
                            "Error occurred while fetching associated sites.",
                            _logger
                        );
                    }
                    catch (Exception ex)
                    {
                        return await ApiResponseUtility.HandleInternalError(
                            cancellationToken,
                            req,
                            ex,
                            "Unexpected error occurred while fetching associated sites.",
                            _logger
                        );
                    }
                }

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    hubsites,
                    ""
                );
            }
            catch (JsonException jsonEx)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    jsonEx,
                    "JSON serialization error occurred while processing the request.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
