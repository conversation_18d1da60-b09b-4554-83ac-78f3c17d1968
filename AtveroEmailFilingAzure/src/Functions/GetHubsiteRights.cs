using System.Net;
using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.CsomClientService;
using AtveroEmailFiling.Services.MSCsomClientContextService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using AtveroEmailFilingAzure.src.Models.ApiResponses;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.SharePoint.Client;

namespace AtveroEmailFilingAzure.src.Functions
{
    public class GetHubsiteRights(
        ILogger<GetHubsiteRights> logger,
        ITokenValidationService tokenValidationService,
        ICsomClientContextService csomClientContextService,
        ICsomApiClient csomApiClient
    )
    {
        private readonly ILogger<GetHubsiteRights> _logger = logger;
        private readonly ICsomClientContextService _csomClientContextService =
            csomClientContextService;
        private readonly ICsomApiClient _csomApiClient = csomApiClient;
        private readonly ITokenValidationService _tokenValidationService = tokenValidationService;

        [Function("GetHubsiteRights")]
        public async Task<HttpResponseData> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation("GetHubsiteRights function triggered");

            try
            {
                // Get sitePath from query parameters
                string? sitePath = req.Query["hubsiteUrl"];

                if (string.IsNullOrEmpty(sitePath))
                {
                    _logger.LogWarning("SitePath parameter is missing");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "hubsiteUrl query parameter is required",
                        _logger
                    );
                }

                _logger.LogInformation($"Getting hub site rights for: {sitePath}");

                // Validate authorization token
                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);
                if (null == token)
                {
                    _logger.LogWarning("Failed to validate authorization token");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to validate authorization token",
                        _logger
                    );
                }

                TokenDetails tokenDetails = _tokenValidationService.GetUserDetails(token);
                if (tokenDetails?.ClientId == null || tokenDetails?.ClientSecret == null)
                {
                    _logger.LogWarning("Token details are incomplete");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Token details are incomplete",
                        _logger
                    );
                }

                _logger.LogInformation($"Request from user: {tokenDetails.Upn}");

                // Extract hostname from sitePath
                string? hostname = null;
                if (!string.IsNullOrEmpty(sitePath))
                {
                    hostname = SharepointUtils.GetTenantFromUrl(sitePath);
                }

                if (hostname == null)
                {
                    _logger.LogWarning($"Unable to extract hostname from sitePath: {sitePath}");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid sitePath format - unable to extract tenant hostname",
                        _logger
                    );
                }

                // Construct admin tenant URL
                string adminTenant = $"https://{hostname}-admin.sharepoint.com";
                _logger.LogInformation($"Using admin tenant: {adminTenant}");

                // Validate URL format
                if (
                    !Uri.TryCreate(sitePath, UriKind.Absolute, out Uri? siteUri)
                    || (siteUri.Scheme != Uri.UriSchemeHttp && siteUri.Scheme != Uri.UriSchemeHttps)
                )
                {
                    _logger.LogWarning($"Invalid sitePath URL format: {sitePath}");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "sitePath must be a valid HTTP or HTTPS URL",
                        _logger
                    );
                }

                // Create CSOM client context for admin operations
                ClientContext? clientContext =
                    await _csomClientContextService.GetClientContextAsync(
                        adminTenant,
                        null,
                        token,
                        tokenDetails.ClientId,
                        tokenDetails.ClientSecret
                    );

                if (clientContext == null)
                {
                    _logger.LogError("Failed to create CSOM client context");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Failed to create SharePoint client context",
                        _logger
                    );
                }

                // Get hub site rights
                Result<List<HubsiteUser>> hubsiteRightsResult =
                    await _csomApiClient.GetHubsiteRights(sitePath, clientContext);

                if (hubsiteRightsResult.IsFailed)
                {
                    _logger.LogError(
                        $"Failed to get hub site rights: {string.Join(", ", hubsiteRightsResult.Errors.Select(e => e.Message))}"
                    );

                    // Check if it's an access denied error
                    IError accessDeniedError = hubsiteRightsResult.Errors.FirstOrDefault(e =>
                        e.Message.Contains("access", StringComparison.OrdinalIgnoreCase)
                        || e.Message.Contains("unauthorized", StringComparison.OrdinalIgnoreCase)
                        || e.Message.Contains("permission", StringComparison.OrdinalIgnoreCase)
                    );

                    if (accessDeniedError != null)
                    {
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.Forbidden,
                            "Insufficient permissions to access hub site rights",
                            _logger
                        );
                    }

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Failed to retrieve hub site rights",
                        _logger
                    );
                }

                List<HubsiteUser> userRights = hubsiteRightsResult.Value;
                _logger.LogInformation(
                    $"Successfully retrieved {userRights.Count} users with hub site rights"
                );

                // Create response object
                HubsiteRightsResponse response = new()
                {
                    SitePath = sitePath,
                    UsersWithRights = userRights,
                };

                // Return the list of users with hub site rights
                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    response,
                    $"Successfully retrieved hub site rights for {userRights.Count} users"
                );
            }
            catch (UriFormatException ex)
            {
                _logger.LogError(ex, "Invalid URL format provided");
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Invalid URL format in sitePath parameter",
                    _logger
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error in GetHubsiteRights function");
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while retrieving hub site rights",
                    _logger
                );
            }
        }
    }
}
