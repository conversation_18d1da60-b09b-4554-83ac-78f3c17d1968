using System.Net;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class GetInfoResponse
    {
        public string ProjectCode { get; set; }
        public string InternetMessageId { get; set; }

        public string? Tag { get; set; }
        public bool Confidential { get; set; }

        public bool Important { get; set; }

        public string? SharedMailbox { get; set; }

        public string FiledBy { get; set; }

        public DateTimeOffset? Timestamp { get; set; }

        public GetInfoResponse(FiledEmail filedEmail)
        {
            ProjectCode = filedEmail.ProjectCode;
            InternetMessageId = filedEmail.PartitionKey;
            Tag = filedEmail.Tag;
            Confidential = filedEmail.Confidential;
            Important = filedEmail.Important;
            FiledBy = filedEmail.FiledBy;
            Timestamp = filedEmail?.Timestamp?.ToUniversalTime();
        }
    }

    public class GetInfo
    {
        private readonly ILogger<GetInfo> _logger;
        private readonly ITokenValidationService _tokenValidationService;

        private readonly IAzureTableService _tableService;

        public GetInfo(
            ILogger<GetInfo> logger,
            ITokenValidationService tokenValidationService,
            IAzureTableService tableService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));
        }

        [Function("GetInfo")]
        public async Task<HttpResponseData?> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            HttpResponseData response = req.CreateResponse();

            try
            {
                string? internetMessageId = req.Query["internetMessageId"];

                if (string.IsNullOrEmpty(internetMessageId))
                {
                    HttpResponseData? res = await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "internetMessgeId is required.",
                        _logger
                    );
                    return res;
                }

                Models.TokenDetails? tokenDetails = null;
                string? token =
                    await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(req);
                if (token != null)
                {
                    tokenDetails = _tokenValidationService.GetUserDetails(token);
                    _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                    SentrySdk.ConfigureScope(scope =>
                    {
                        scope.User = new SentryUser
                        {
                            Id = tokenDetails.Upn,
                            Email = tokenDetails.Upn,
                        };
                    });
                    _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");
                }

                if (null == tokenDetails)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Unable to extract token details from the request.",
                        _logger
                    );
                }

                string customerDomainRaw = EmailUtils.ExtractDomainFromEmailOrUpn(
                    tokenDetails.Upn,
                    _logger
                );

                string customerDomain = customerDomainRaw;

                string? mappedDomain = await DomainUtils.MapDomain(
                    customerDomainRaw,
                    _tableService
                );
                if (mappedDomain != null)
                {
                    _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                    customerDomain = mappedDomain;
                }

                string transformedMessageId = EmailUtils.CleanMessageId(internetMessageId);

                string filter = $"PartitionKey eq '{transformedMessageId}'";

                _logger.LogInformation("filter is " + filter);
                _logger.LogInformation("customerDomain is " + customerDomain);

                List<AtveroEmailFiling.Models.AzureTables.FiledEmail> filedEntries =
                    await _tableService.QueryEntitiesAsync<FiledEmail>(
                        "FiledEmails",
                        filter,
                        customerDomain
                    );

                _logger.LogInformation("found " + filedEntries.Count + " entries");

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    filedEntries.Select(f => new GetInfoResponse(f)).ToList(),
                    customerDomain
                );
            }
            catch (JsonException jsonEx)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    jsonEx,
                    "JSON serialization error occurred while processing the request.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
