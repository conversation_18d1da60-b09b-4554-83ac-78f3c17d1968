using System.Net;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using AtveroEmailFilingAzure.src.Services.SharepointApi;
using FluentResults;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Functions
{
    public class CreateSiteNoGroup
    {
        private readonly ILogger<Hubsites> _logger;
        private readonly IGraphClientService _graphClientService;
        private readonly ITokenValidationService _tokenValidationService;

        private readonly IGraphApiClient _graphApiClient;

        private readonly ISharepointApiFactory _sharepointApiFactory;

        private readonly IAzureTableService _tableService;

        public CreateSiteNoGroup(
            ILogger<Hubsites> logger,
            ITokenValidationService tokenValidationService,
            IGraphClientService graphClientService,
            IGraphApiClient graphApiClient,
            ISharepointApiFactory sharepointApiFactory,
            IAzureTableService tableService
        )
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));

            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _sharepointApiFactory =
                sharepointApiFactory
                ?? throw new ArgumentNullException(nameof(_sharepointApiFactory));
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));
        }

        [Function("CreateSiteNoGroup")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            try
            {
                string requestBody;
                using (var streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                    // _logger.LogDebug("Request body: {RequestBody}", requestBody);
                }

                var createSiteRequest = JsonConvert.DeserializeObject<CreateSiteRequest>(
                    requestBody
                );
                if (createSiteRequest == null)
                {
                    _logger.LogWarning("Deserialized request data is null.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid request data.",
                        _logger
                    );
                }

                string tenant = null;

                if (createSiteRequest.HubSite.StartsWith("https"))
                {
                    tenant = SharepointUtils.GetFullTenantFromUrl(createSiteRequest.HubSite);
                }
                else
                {
                    Result<string> tenantRes = await FunctionUtils.GetTenantAsync(
                        req,
                        _logger,
                        _tokenValidationService,
                        _graphApiClient,
                        _graphClientService
                    );

                    if (tenantRes.IsSuccess)
                    {
                        tenant = tenantRes.Value;
                    }
                }

                if (tenant == null)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Failed to obtain tenant",
                        _logger
                    );
                }

                string? token = await _tokenValidationService.ValidateAuthorizationHeaderAsync(req);

                if (null == token)
                {
                    SentrySdk.CaptureMessage("Failed to validate authorization token.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to validate authorization token.",
                        _logger
                    );
                }

                var tokenDetails = _tokenValidationService.GetUserDetails(token);

                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                SharepointApi? sharepointApi = await _sharepointApiFactory.CreateSharepointApiAsync(
                    req,
                    tenant,
                    token,
                    tokenDetails.ClientId,
                    tokenDetails.ClientSecret,
                    _logger
                );

                if (null == sharepointApi)
                {
                    SentrySdk.CaptureMessage("Failed to create SharePoint API.");

                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Failed to create SharePoint API.",
                        _logger
                    );
                }

                string customerDomainRaw = EmailUtils.ExtractDomainFromEmailOrUpn(
                    tokenDetails.Upn,
                    _logger
                );

                string customerDomain = customerDomainRaw;

                string? mappedDomain = await DomainUtils.MapDomain(
                    customerDomainRaw,
                    _tableService
                );
                if (mappedDomain != null)
                {
                    _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                    customerDomain = mappedDomain;
                }

                if (
                    string.IsNullOrEmpty(createSiteRequest.HubSite)
                    || string.IsNullOrEmpty(createSiteRequest.SiteName)
                )
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid request data.",
                        _logger
                    );
                }

                Result<SharepointApi.CreateSiteResponse> newSite = await sharepointApi.CreateSite(
                    createSiteRequest.HubSite,
                    createSiteRequest.SiteName,
                    createSiteRequest.Description,
                    createSiteRequest.HubSiteId
                );

                if (newSite.IsFailed)
                {
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "Unexpected error occurred whilst creating site",
                        _logger
                    );
                }

                return await ApiResponseUtility.CreateSuccessResponse(
                    cancellationToken,
                    req,
                    newSite,
                    ""
                );
            }
            catch (Exception ex)
            {
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "An unexpected error occurred while processing the request.",
                    _logger
                );
            }
        }
    }
}
