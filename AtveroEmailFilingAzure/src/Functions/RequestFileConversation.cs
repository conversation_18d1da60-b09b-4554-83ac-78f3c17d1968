using System.Net;
using System.Text;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Models.QueueMessages;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.FilingStatusCheck;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using Azure.Storage.Queues;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Atvero.Mail
{
    public class RequestFileConversation
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<EnqueueEmailRequest> _logger;
        private readonly IConfiguration _configuration;

        private readonly IFilingStatusCheckService _filingStatusCheckService;

        public RequestFileConversation(
            ITokenValidationService tokenValidationService,
            IFilingStatusCheckService filingStatusCheckService,
            ILogger<EnqueueEmailRequest> logger,
            IConfiguration configuration
        )
        {
            _filingStatusCheckService = filingStatusCheckService;

            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        [Function("RequestFileConversation")]
        public async Task<HttpResponseData?> RunAsync(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to file a conversation {Time}.",
                DateTime.UtcNow
            );

            try
            {
                // make sure we can connect to the queue otherwise no point in doing all this work

                // Create QueueClient
                string? connectionString = _configuration["AzureWebJobsStorage"];

                // Authenticate and get token

                var token = await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(
                    req
                );
                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogWarning("Failed to get a valid token.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to get a valid token.",
                        _logger
                    );
                }

                var tokenDetails = _tokenValidationService.GetUserDetails(token);
                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                string customerDomain = ExtractDomainFromEmailOrUpn(tokenDetails.Upn, _logger);
                string user = tokenDetails.Upn ?? "unknown_user";

                // Read and log the request body
                string requestBody;
                using (var streamReader = new StreamReader(req.Body))
                {
                    requestBody = await streamReader.ReadToEndAsync();
                    // _logger.LogDebug("Request body: {RequestBody}", requestBody);
                }

                FileConversationRequest emailRequest =
                    JsonConvert.DeserializeObject<FileConversationRequest>(requestBody);
                if (
                    emailRequest == null
                    || emailRequest.ConversationId == null
                    || emailRequest.ProjectCode == null
                )
                {
                    _logger.LogWarning("Deserialized request data is null.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.BadRequest,
                        "Invalid request data.",
                        _logger
                    );
                }

                string activeUser = string.IsNullOrEmpty(emailRequest.SharedMailbox)
                    ? user
                    : emailRequest.SharedMailbox;

                //  we add the message conversation ID to the conversation table.


                await EmailUtils.MarkConversationForFiling(
                    _filingStatusCheckService,
                    user,
                    emailRequest.ConversationId,
                    emailRequest.ProjectCode,
                    emailRequest.Tag,
                    emailRequest.Confidential,
                    emailRequest.Important,
                    activeUser,
                    customerDomain
                );

                // we don't trigger any processing, as the message won't be in Sent Items
                // wait on the next run for it to be picked up

                return await ApiResponseUtility.CreateSuccessResponse<string>(
                    cancellationToken,
                    req,
                    "OK",
                    "Conversation successfully added."
                );
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "Argument exception occurred.");
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Invalid argument provided.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process sync request.");
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "Failed to process email request.",
                    _logger
                );
            }
        }

        public static string ExtractDomainFromEmailOrUpn(string emailOrUpn, ILogger logger)
        {
            if (string.IsNullOrWhiteSpace(emailOrUpn))
            {
                logger.LogError("Email or UPN cannot be null or empty.");
                throw new ArgumentException(
                    "Email or UPN cannot be null or empty.",
                    nameof(emailOrUpn)
                );
            }

            var parts = emailOrUpn.Split('@');
            if (parts.Length != 2)
            {
                logger.LogError("The provided email or UPN is not in a valid format.");
                throw new FormatException("The provided email or UPN is not in a valid format.");
            }

            var domain = parts[1].Trim();
            if (
                string.IsNullOrWhiteSpace(domain) || !domain.Contains('.', StringComparison.Ordinal)
            )
            {
                logger.LogError("The domain part of the email or UPN is invalid.");
                throw new FormatException("The domain part of the email or UPN is invalid.");
            }

            string sanitizedDomain = EmailUtils.SanitizeCustomerDomain(domain);
            logger.LogInformation("Sanitized domain: {SanitizedDomain}", sanitizedDomain);
            return sanitizedDomain;
        }
    }
}
