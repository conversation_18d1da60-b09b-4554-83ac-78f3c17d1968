using System.Net;
using System.Text;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Models.QueueMessages;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using Azure.Storage.Queues;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace Atvero.Mail
{
    public class SyncEmails
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<EnqueueEmailRequest> _logger;
        private readonly IConfiguration _configuration;

        private readonly IAzureTableService _tableService;

        public SyncEmails(
            IAzureTableService tableService,
            ITokenValidationService tokenValidationService,
            ILogger<EnqueueEmailRequest> logger,
            IConfiguration configuration
        )
        {
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));

            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        [Function("SyncEmails")]
        public async Task<HttpResponseData?> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation("Received a request to sync status {Time}.", DateTime.UtcNow);

            try
            {
                // make sure we can connect to the queue otherwise no point in doing all this work

                // Create QueueClient
                string? connectionString = _configuration["AzureWebJobsStorage"];

                string fboQueueName = "filedbyothersqueue";
                var fboQueueClient = new QueueClient(connectionString, fboQueueName);
                // Ensure the queue exists (optional)
                var createFBOResult = await fboQueueClient.CreateIfNotExistsAsync();
                if (createFBOResult != null)
                {
                    _logger.LogInformation("Queue {QueueName} created.", fboQueueName);
                }
                else
                {
                    _logger.LogInformation("Queue {QueueName} already exists.", fboQueueName);
                }

                string ffQueueName = "futurefilingqueue";
                var ffQueueClient = new QueueClient(connectionString, ffQueueName);
                // Ensure the queue exists (optional)
                var createFFResult = await ffQueueClient.CreateIfNotExistsAsync();
                if (createFFResult != null)
                {
                    _logger.LogInformation("Queue {QueueName} created.", ffQueueName);
                }
                else
                {
                    _logger.LogInformation("Queue {QueueName} already exists.", ffQueueName);
                }

                string fosQueueName = "fileonsendqueue";
                var fosQueueClient = new QueueClient(connectionString, fosQueueName);
                // Ensure the queue exists (optional)
                var createFOSResult = await fosQueueClient.CreateIfNotExistsAsync();
                if (createFOSResult != null)
                {
                    _logger.LogInformation("Queue {QueueName} created.", fosQueueName);
                }
                else
                {
                    _logger.LogInformation("Queue {QueueName} already exists.", fosQueueName);
                }

                // Authenticate and get token

                var token = await _tokenValidationService.ValidateCommonAuthorizationHeaderAsync(
                    req
                );
                if (string.IsNullOrEmpty(token))
                {
                    _logger.LogWarning("Failed to get a valid token.");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.Unauthorized,
                        "Failed to get a valid token.",
                        _logger
                    );
                }

                var tokenDetails = _tokenValidationService.GetUserDetails(token);
                SentrySdk.ConfigureScope(scope =>
                {
                    scope.User = new SentryUser { Id = tokenDetails.Upn, Email = tokenDetails.Upn };
                });

                _logger.LogInformation("Running as " + tokenDetails.Upn ?? "");

                string customerDomain = ExtractDomainFromEmailOrUpn(tokenDetails.Upn, _logger);
                string user = tokenDetails.Upn ?? "unknown_user";

                if (Environment.GetEnvironmentVariable("ENABLE_FILED_BY_OTHERS") == "true")
                {
                    var fboMessage = new FiledByOthersQueueMessage
                    {
                        Token = token,
                        UserId = tokenDetails.Upn ?? "unknown_user",
                        CustomerDomain = customerDomain,
                    };

                    // Serialize the queue message and base64 encode it
                    var fboMessageString = JsonConvert.SerializeObject(fboMessage);
                    var base64FBOMessage = Convert.ToBase64String(
                        Encoding.UTF8.GetBytes(fboMessageString)
                    );
                    try
                    {
                        await fboQueueClient.SendMessageAsync(base64FBOMessage);
                        _logger.LogInformation("Message processing queue message enqueued.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Failed to enqueue message to {QueueName}.",
                            fboQueueName
                        );
                    }
                }

                if (Environment.GetEnvironmentVariable("ENABLE_FUTURE_FILING") == "true")
                {
                    var ffMessage = new FutureFilingQueueMessage
                    {
                        Token = token,
                        UserId = tokenDetails.Upn ?? "unknown_user",
                        CustomerDomain = customerDomain,
                    };

                    // Serialize the queue message and base64 encode it
                    var ffMessageString = JsonConvert.SerializeObject(ffMessage);
                    var base64FFMessage = Convert.ToBase64String(
                        Encoding.UTF8.GetBytes(ffMessageString)
                    );
                    try
                    {
                        await ffQueueClient.SendMessageAsync(base64FFMessage);
                        _logger.LogInformation("Future filing processing queue message enqueued.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Failed to enqueue message to {QueueName}.",
                            ffQueueName
                        );
                    }
                }

                if (Environment.GetEnvironmentVariable("ENABLE_FILE_ON_SEND") == "true")
                {
                    var fosMessage = new FileOnSendQueueMessage
                    {
                        Token = token,
                        UserId = tokenDetails.Upn ?? "unknown_user",
                        CustomerDomain = customerDomain,
                    };

                    // Serialize the queue message and base64 encode it
                    var fosMessageString = JsonConvert.SerializeObject(fosMessage);
                    var base64FOSMessage = Convert.ToBase64String(
                        Encoding.UTF8.GetBytes(fosMessageString)
                    );
                    try
                    {
                        await fosQueueClient.SendMessageAsync(base64FOSMessage);
                        _logger.LogInformation("Future filing processing queue message enqueued.");
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError(
                            ex,
                            "Failed to enqueue message to {QueueName}.",
                            fosQueueName
                        );
                    }
                }

                return await ApiResponseUtility.CreateSuccessResponse<string>(
                    cancellationToken,
                    req,
                    "OK",
                    "Request successfully enqueued."
                );
            }
            catch (ArgumentException ex)
            {
                _logger.LogError(ex, "Argument exception occurred.");
                return await ApiResponseUtility.CreateErrorResponse<string>(
                    cancellationToken,
                    req,
                    HttpStatusCode.BadRequest,
                    "Invalid argument provided.",
                    _logger
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to process sync request.");
                return await ApiResponseUtility.HandleInternalError(
                    cancellationToken,
                    req,
                    ex,
                    "Failed to process email request.",
                    _logger
                );
            }
        }

        public static string ExtractDomainFromEmailOrUpn(string emailOrUpn, ILogger logger)
        {
            if (string.IsNullOrWhiteSpace(emailOrUpn))
            {
                logger.LogError("Email or UPN cannot be null or empty.");
                throw new ArgumentException(
                    "Email or UPN cannot be null or empty.",
                    nameof(emailOrUpn)
                );
            }

            var parts = emailOrUpn.Split('@');
            if (parts.Length != 2)
            {
                logger.LogError("The provided email or UPN is not in a valid format.");
                throw new FormatException("The provided email or UPN is not in a valid format.");
            }

            var domain = parts[1].Trim();
            if (
                string.IsNullOrWhiteSpace(domain) || !domain.Contains('.', StringComparison.Ordinal)
            )
            {
                logger.LogError("The domain part of the email or UPN is invalid.");
                throw new FormatException("The domain part of the email or UPN is invalid.");
            }

            string sanitizedDomain = EmailUtils.SanitizeCustomerDomain(domain);
            logger.LogInformation("Sanitized domain: {SanitizedDomain}", sanitizedDomain);
            return sanitizedDomain;
        }
    }
}
