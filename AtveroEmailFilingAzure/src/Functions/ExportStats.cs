using System.Net;
using System.Text;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace AtveroEmailFiling.Functions
{
    // Enum for stats types to replace magic strings
    public enum StatsType
    {
        UserStats,
        DomainStats,
        AutoFilingStats,
        UserClickStats,
        ProjectStats,
    }

    public class ExportStats
    {
        private readonly ILogger<ExportStats> _logger;
        private readonly IAzureTableService _tableService;
        private readonly IConfiguration _configuration;

        public ExportStats(
            ILogger<ExportStats> logger,
            IAzureTableService tableService,
            IConfiguration configuration
        )
        {
            _logger = logger;
            _tableService = tableService;
            _configuration = configuration;
        }

        [Function("ExportStats")]
        public async Task<HttpResponseData> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get")] HttpRequestData req
        )
        {
            _logger.LogInformation("ExportStats function triggered at {Time}", DateTime.UtcNow);

            try
            {
                // Validate shared API key
                if (!FunctionUtils.ValidateApiKey(req, _configuration, _logger, "ExportStats"))
                {
                    _logger.LogWarning("ExportStats access denied - invalid or missing API key");
                    var errorResponse = req.CreateResponse(HttpStatusCode.Unauthorized);
                    await errorResponse.WriteStringAsync("Unauthorized - Invalid API key");
                    return errorResponse;
                }

                // Parse query parameters
                var exportRequest = ParseQueryParameters(req);
                if (exportRequest == null)
                {
                    var errorResponse = req.CreateResponse(HttpStatusCode.BadRequest);
                    await errorResponse.WriteStringAsync("Invalid query parameters");
                    return errorResponse;
                }

                // Export statistics
                var csvContent = await ExportStatistics(exportRequest);

                // Create CSV response
                var response = req.CreateResponse(HttpStatusCode.OK);
                response.Headers.Add("Content-Type", "text/csv");
                response.Headers.Add(
                    "Content-Disposition",
                    $"attachment; filename=\"{GenerateFileName(exportRequest)}\""
                );

                await response.WriteStringAsync(csvContent);
                return response;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error exporting statistics");
                var errorResponse = req.CreateResponse(HttpStatusCode.InternalServerError);
                await errorResponse.WriteStringAsync("Internal server error");
                return errorResponse;
            }
        }

        private ExportStatsRequest? ParseQueryParameters(HttpRequestData req)
        {
            var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);

            var statsType = query["statsType"];
            var period = query["period"];
            var startYearStr = query["startYear"];
            var endYearStr = query["endYear"];
            var startWeekStr = query["startWeek"];
            var endWeekStr = query["endWeek"];
            var startMonthStr = query["startMonth"];
            var endMonthStr = query["endMonth"];
            var weekStr = query["week"];
            var monthStr = query["month"];

            // Validate required parameters
            if (
                string.IsNullOrEmpty(statsType)
                || string.IsNullOrEmpty(period)
                || string.IsNullOrEmpty(startYearStr)
            )
            {
                return null;
            }

            if (!int.TryParse(startYearStr, out int startYear))
            {
                return null;
            }

            var request = new ExportStatsRequest
            {
                StatsType = statsType,
                Period = period.ToLower(),
                StartYear = startYear,
                EndYear = int.TryParse(endYearStr, out int endYear) ? endYear : startYear,
            };

            // Parse week/month parameters
            if (period.ToLower() == "weekly")
            {
                if (!string.IsNullOrEmpty(weekStr))
                {
                    if (int.TryParse(weekStr, out int week))
                    {
                        request.StartWeek = week;
                        request.EndWeek = week;
                    }
                }
                else
                {
                    if (int.TryParse(startWeekStr, out int startWeek))
                    {
                        request.StartWeek = startWeek;
                        request.EndWeek = int.TryParse(endWeekStr, out int endWeek)
                            ? endWeek
                            : startWeek;
                    }
                }
            }
            else if (period.ToLower() == "monthly")
            {
                if (!string.IsNullOrEmpty(monthStr))
                {
                    if (int.TryParse(monthStr, out int month))
                    {
                        request.StartMonth = month;
                        request.EndMonth = month;
                    }
                }
                else
                {
                    if (int.TryParse(startMonthStr, out int startMonth))
                    {
                        request.StartMonth = startMonth;
                        request.EndMonth = int.TryParse(endMonthStr, out int endMonth)
                            ? endMonth
                            : startMonth;
                    }
                }
            }

            return request;
        }

        private async Task<string> ExportStatistics(ExportStatsRequest request)
        {
            var csvBuilder = new StringBuilder();

            // Parse stats type enum
            if (!Enum.TryParse<StatsType>(request.StatsType, true, out var statsType))
            {
                throw new ArgumentException($"Unknown stats type: {request.StatsType}");
            }

            // Add CSV header
            csvBuilder.AppendLine(GetCsvHeader(statsType, request.Period));

            // Build filters and query data
            var filters = BuildFilters(request);

            foreach (var filter in filters)
            {
                var tableName = GetTableName(statsType, request.Period);
                await ExportStatsGeneric(csvBuilder, tableName, filter, request.Period, statsType);
            }

            return csvBuilder.ToString();
        }

        private List<string> BuildFilters(ExportStatsRequest request)
        {
            var filters = new List<string>();

            // Parse stats type to determine filter pattern
            var isDomainStats =
                Enum.TryParse<StatsType>(request.StatsType, true, out var statsType)
                && statsType == StatsType.DomainStats;

            if (request.Period == "weekly")
            {
                for (int year = request.StartYear; year <= request.EndYear; year++)
                {
                    int startWeek = (year == request.StartYear) ? (request.StartWeek ?? 1) : 1;
                    int endWeek = (year == request.EndYear) ? (request.EndWeek ?? 53) : 53;

                    for (int week = startWeek; week <= endWeek; week++)
                    {
                        string weekPrefix = $"{year:D4}-{week:D2}";

                        // For domain stats, partition key is just YYYY-WW
                        if (isDomainStats)
                        {
                            filters.Add($"PartitionKey eq '{weekPrefix}'");
                        }
                        else
                        {
                            // For other stats, partition key is YYYY-WW_CustomerDomain
                            filters.Add(
                                $"PartitionKey ge '{weekPrefix}_' and PartitionKey lt '{weekPrefix}~'"
                            );
                        }
                    }
                }
            }
            else if (request.Period == "monthly")
            {
                for (int year = request.StartYear; year <= request.EndYear; year++)
                {
                    int startMonth = (year == request.StartYear) ? (request.StartMonth ?? 1) : 1;
                    int endMonth = (year == request.EndYear) ? (request.EndMonth ?? 12) : 12;

                    for (int month = startMonth; month <= endMonth; month++)
                    {
                        string monthPrefix = $"{year:D4}-{month:D2}";

                        // For domain stats, partition key is just YYYY-MM
                        if (isDomainStats)
                        {
                            filters.Add($"PartitionKey eq '{monthPrefix}'");
                        }
                        else
                        {
                            // For other stats, partition key is YYYY-MM_CustomerDomain
                            filters.Add(
                                $"PartitionKey ge '{monthPrefix}_' and PartitionKey lt '{monthPrefix}~'"
                            );
                        }
                    }
                }
            }

            return filters;
        }

        private static string GetTableName(StatsType statsType, string period)
        {
            var prefix = period == "weekly" ? "AAWeekly" : "AAMonthly";

            return statsType switch
            {
                StatsType.UserStats => $"{prefix}UserStats",
                StatsType.DomainStats => $"{prefix}DomainStats",
                StatsType.AutoFilingStats => $"{prefix}AutoFilingStats",
                StatsType.UserClickStats => $"{prefix}UserClickStats",
                StatsType.ProjectStats => $"{prefix}ProjectStats",
                _ => throw new ArgumentException($"Unknown stats type: {statsType}"),
            };
        }

        private static string GetCsvHeader(StatsType statsType, string period)
        {
            var periodColumn = period == "weekly" ? "WeeklyCount" : "MonthlyCount";

            return statsType switch
            {
                StatsType.DomainStats => $"Period,CustomerDomain,{periodColumn}",
                _ => $"Period,CustomerDomain,RowKey,{periodColumn}",
            };
        }

        private string GenerateFileName(ExportStatsRequest request)
        {
            var timestamp = DateTime.UtcNow.ToString("yyyyMMdd_HHmmss");
            var periodRange =
                request.Period == "weekly"
                    ? $"W{request.StartWeek ?? 1}-{request.EndWeek ?? 53}"
                    : $"M{request.StartMonth ?? 1}-{request.EndMonth ?? 12}";

            return $"{request.StatsType}_{request.Period}_{request.StartYear}-{request.EndYear}_{periodRange}_{timestamp}.csv";
        }

        private async Task ExportStatsGeneric(
            StringBuilder csvBuilder,
            string tableName,
            string filter,
            string period,
            StatsType statsType
        )
        {
            if (period == "weekly")
            {
                await ExportWeeklyStats(csvBuilder, tableName, filter, statsType);
            }
            else
            {
                await ExportMonthlyStats(csvBuilder, tableName, filter, statsType);
            }
        }

        private async Task ExportWeeklyStats(
            StringBuilder csvBuilder,
            string tableName,
            string filter,
            StatsType statsType
        )
        {
            switch (statsType)
            {
                case StatsType.UserStats:
                    await ExportWeeklyStatsTyped<WeeklyUserStats>(
                        csvBuilder,
                        tableName,
                        filter,
                        false
                    );
                    break;
                case StatsType.DomainStats:
                    await ExportWeeklyStatsTyped<WeeklyDomainStats>(
                        csvBuilder,
                        tableName,
                        filter,
                        true
                    );
                    break;
                case StatsType.AutoFilingStats:
                    await ExportWeeklyStatsTyped<WeeklyAutoFilingStats>(
                        csvBuilder,
                        tableName,
                        filter,
                        false
                    );
                    break;
                case StatsType.UserClickStats:
                    await ExportWeeklyStatsTyped<WeeklyUserClickStats>(
                        csvBuilder,
                        tableName,
                        filter,
                        false
                    );
                    break;
                case StatsType.ProjectStats:
                    await ExportWeeklyStatsTyped<WeeklyProjectStats>(
                        csvBuilder,
                        tableName,
                        filter,
                        false
                    );
                    break;
                default:
                    throw new ArgumentException($"Unknown stats type: {statsType}");
            }
        }

        private async Task ExportMonthlyStats(
            StringBuilder csvBuilder,
            string tableName,
            string filter,
            StatsType statsType
        )
        {
            switch (statsType)
            {
                case StatsType.UserStats:
                    await ExportMonthlyStatsTyped<MonthlyUserStats>(
                        csvBuilder,
                        tableName,
                        filter,
                        false
                    );
                    break;
                case StatsType.DomainStats:
                    await ExportMonthlyStatsTyped<MonthlyDomainStats>(
                        csvBuilder,
                        tableName,
                        filter,
                        true
                    );
                    break;
                case StatsType.AutoFilingStats:
                    await ExportMonthlyStatsTyped<MonthlyAutoFilingStats>(
                        csvBuilder,
                        tableName,
                        filter,
                        false
                    );
                    break;
                case StatsType.UserClickStats:
                    await ExportMonthlyStatsTyped<MonthlyUserClickStats>(
                        csvBuilder,
                        tableName,
                        filter,
                        false
                    );
                    break;
                case StatsType.ProjectStats:
                    await ExportMonthlyStatsTyped<MonthlyProjectStats>(
                        csvBuilder,
                        tableName,
                        filter,
                        false
                    );
                    break;
                default:
                    throw new ArgumentException($"Unknown stats type: {statsType}");
            }
        }

        private async Task ExportWeeklyStatsTyped<T>(
            StringBuilder csvBuilder,
            string tableName,
            string filter,
            bool isDomainStats
        )
            where T : class, Azure.Data.Tables.ITableEntity
        {
            var stats = await _tableService.QueryEntitiesAsync<T>(tableName, filter, "");

            foreach (var stat in stats)
            {
                if (isDomainStats)
                {
                    // For domain stats: Period, CustomerDomain, Count
                    var weeklyCount = GetWeeklyCount(stat);
                    csvBuilder.AppendLine(
                        $"{stat.PartitionKey},{EscapeCsv(stat.RowKey)},{weeklyCount}"
                    );
                }
                else
                {
                    // For other stats: Period, CustomerDomain, RowKey, Count
                    var parts = stat.PartitionKey.Split('_');
                    var periodPart = parts[0];
                    var customerDomain = parts.Length > 1 ? parts[1] : "";
                    var weeklyCount = GetWeeklyCount(stat);
                    csvBuilder.AppendLine(
                        $"{periodPart},{EscapeCsv(customerDomain)},{EscapeCsv(stat.RowKey)},{weeklyCount}"
                    );
                }
            }
        }

        private async Task ExportMonthlyStatsTyped<T>(
            StringBuilder csvBuilder,
            string tableName,
            string filter,
            bool isDomainStats
        )
            where T : class, Azure.Data.Tables.ITableEntity
        {
            var stats = await _tableService.QueryEntitiesAsync<T>(tableName, filter, "");

            foreach (var stat in stats)
            {
                if (isDomainStats)
                {
                    // For domain stats: Period, CustomerDomain, Count
                    var monthlyCount = GetMonthlyCount(stat);
                    csvBuilder.AppendLine(
                        $"{stat.PartitionKey},{EscapeCsv(stat.RowKey)},{monthlyCount}"
                    );
                }
                else
                {
                    // For other stats: Period, CustomerDomain, RowKey, Count
                    var parts = stat.PartitionKey.Split('_');
                    var periodPart = parts[0];
                    var customerDomain = parts.Length > 1 ? parts[1] : "";
                    var monthlyCount = GetMonthlyCount(stat);
                    csvBuilder.AppendLine(
                        $"{periodPart},{EscapeCsv(customerDomain)},{EscapeCsv(stat.RowKey)},{monthlyCount}"
                    );
                }
            }
        }

        private static int GetWeeklyCount(object stat)
        {
            return stat switch
            {
                WeeklyUserStats weeklyUserStat => weeklyUserStat.WeeklyCount,
                WeeklyDomainStats weeklyDomainStat => weeklyDomainStat.WeeklyCount,
                WeeklyAutoFilingStats weeklyAutoStat => weeklyAutoStat.WeeklyCount,
                WeeklyUserClickStats weeklyClickStat => weeklyClickStat.WeeklyCount,
                WeeklyProjectStats weeklyProjectStat => weeklyProjectStat.WeeklyCount,
                _ => throw new ArgumentException($"Unknown weekly stats type: {stat.GetType()}"),
            };
        }

        private static int GetMonthlyCount(object stat)
        {
            return stat switch
            {
                MonthlyUserStats monthlyUserStat => monthlyUserStat.MonthlyCount,
                MonthlyDomainStats monthlyDomainStat => monthlyDomainStat.MonthlyCount,
                MonthlyAutoFilingStats monthlyAutoStat => monthlyAutoStat.MonthlyCount,
                MonthlyUserClickStats monthlyClickStat => monthlyClickStat.MonthlyCount,
                MonthlyProjectStats monthlyProjectStat => monthlyProjectStat.MonthlyCount,
                _ => throw new ArgumentException($"Unknown monthly stats type: {stat.GetType()}"),
            };
        }

        private static string EscapeCsv(string value)
        {
            if (string.IsNullOrEmpty(value))
                return "";

            if (
                value.Contains(',')
                || value.Contains('"')
                || value.Contains('\n')
                || value.Contains('\r')
            )
            {
                return $"\"{value.Replace("\"", "\"\"")}\"";
            }

            return value;
        }
    }

    public class ExportStatsRequest
    {
        public required string StatsType { get; set; }
        public required string Period { get; set; }
        public required int StartYear { get; set; }
        public required int EndYear { get; set; }
        public int? StartWeek { get; set; }
        public int? EndWeek { get; set; }
        public int? StartMonth { get; set; }
        public int? EndMonth { get; set; }
    }
}
