using System.Net;
using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.MSGraphClientService;
using AtveroEmailFiling.Services.TokenValidation;
using AtveroEmailFiling.Utils;
using AtveroEmailFilingAzure.Models.ApiRequests;
using Azure.Identity;
using Azure.Security.KeyVault.Secrets;
using FluentResults;
using Microsoft.AspNetCore.DataProtection;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Graph.Models;
using Newtonsoft.Json;

namespace Atvero.Mail
{
    public class StoreUserCredentials
    {
        private readonly ITokenValidationService _tokenValidationService;
        private readonly ILogger<EnqueueEmailRequest> _logger;
        private readonly IConfiguration _configuration;

        private readonly IAzureTableService _tableService;

        private readonly IGraphClientService _graphClientService;

        public StoreUserCredentials(
            IAzureTableService tableService,
            ITokenValidationService tokenValidationService,
            ILogger<EnqueueEmailRequest> logger,
            IGraphClientService graphClientService,
            IConfiguration configuration
        )
        {
            _tableService = tableService ?? throw new ArgumentNullException(nameof(tableService));

            _tokenValidationService =
                tokenValidationService
                ?? throw new ArgumentNullException(nameof(tokenValidationService));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
            _graphClientService =
                graphClientService ?? throw new ArgumentNullException(nameof(graphClientService));
        }

        [Function("StoreUserCredentials")]
        public async Task<HttpResponseData?> Run(
            [HttpTrigger(AuthorizationLevel.Anonymous, "get", "post")] HttpRequestData req,
            CancellationToken cancellationToken
        )
        {
            _logger.LogInformation(
                "Received a request to store a user credential {Time}.",
                DateTime.UtcNow
            );
            try
            {
                string? kvUrl = _configuration["AUTOTOKEN_VAULT_URL"];

                if (kvUrl == null)
                {
                    _logger.LogInformation("AUTOTOKEN_VAULT_URL not configured");
                    return await ApiResponseUtility.CreateErrorResponse<string>(
                        cancellationToken,
                        req,
                        HttpStatusCode.InternalServerError,
                        "AUTOTOKEN_VAULT_URL not configured",
                        _logger
                    );
                }

                System.Collections.Specialized.NameValueCollection? query =
                    System.Web.HttpUtility.ParseQueryString(req.Url.Query);
                string? code = query["code"];
                string? state = query["state"];
                StoreCredientsClientState decodedState = StoreCredientsClientState.Parse(
                    _logger,
                    System.Uri.UnescapeDataString(state),
                    _configuration
                );

                _logger.LogInformation("Decoded State is " + decodedState);

                string clientSecret =
                    WebhookUtils.GetClientSecret(_logger, _configuration, decodedState.ClientId)
                    ?? "";

                string scope =
                    WebhookUtils.GetScope(_logger, _configuration, decodedState.ClientId) ?? "";

                if (code != null)
                {
                    // swap the code for a token
                    Dictionary<string, string> dict = new Dictionary<string, string>();
                    dict.Add("client_id", decodedState.ClientId);
                    dict.Add("scope", scope);
                    dict.Add("code", code);
                    dict.Add("redirect_uri", _configuration["redirect_uri"]);
                    dict.Add("grant_type", "authorization_code");
                    dict.Add("client_secret", clientSecret);

                    HttpClient client = new HttpClient();
                    HttpRequestMessage codeReq = new HttpRequestMessage(
                        HttpMethod.Post,
                        "https://login.microsoftonline.com/common/oauth2/v2.0/token"
                    )
                    {
                        Content = new FormUrlEncodedContent(dict),
                    };
                    HttpResponseMessage? codeRes = await client.SendAsync(codeReq);

                    if (!codeRes.IsSuccessStatusCode)
                    {
                        _logger.LogInformation("Failed to use code get refresh token");
                        string reason = await codeRes.Content.ReadAsStringAsync();
                        _logger.LogInformation(reason);
                        return await ApiResponseUtility.CreateErrorResponse<string>(
                            cancellationToken,
                            req,
                            HttpStatusCode.BadRequest,
                            "Failed to use code to get refresh token",
                            _logger
                        );
                    }
                    else
                    {
                        string tokenJson = await codeRes.Content.ReadAsStringAsync();

                        TokenResponse? tr = JsonConvert.DeserializeObject<TokenResponse>(tokenJson);

                        string? accessToken = tr?.access_token;

                        if (accessToken == null)
                        {
                            _logger.LogInformation("Failed to decode access token");
                            return await ApiResponseUtility.CreateErrorResponse<string>(
                                cancellationToken,
                                req,
                                HttpStatusCode.BadRequest,
                                "Failed to decode access token",
                                _logger
                            );
                        }

                        // use the access token to create a subscription

                        string sharedSecret = Guid.NewGuid().ToString();
                        string bearerToken = $"{accessToken}";

                        AtveroEmailFiling.Models.TokenDetails? tokenDetails =
                            _tokenValidationService.GetUserDetails(bearerToken);

                        Microsoft.Graph.GraphServiceClient? graphClient =
                            _graphClientService.GetUserGraphClient(
                                accessToken,
                                tokenDetails.ClientId,
                                tokenDetails.ClientSecret
                            );

                        if (graphClient == null)
                        {
                            _logger.LogInformation("Failed to use access token to get graph token");
                            return await ApiResponseUtility.CreateErrorResponse<string>(
                                cancellationToken,
                                req,
                                HttpStatusCode.BadRequest,
                                "Failed to use access token to get graph token",
                                _logger
                            );
                        }

                        string customerDomainRaw = EmailUtils.ExtractDomainFromEmailOrUpn(
                            tokenDetails.Upn,
                            _logger
                        );
                        string customerDomain = customerDomainRaw;

                        string? mappedDomain = await DomainUtils.MapDomain(
                            customerDomainRaw,
                            _tableService
                        );
                        if (mappedDomain != null)
                        {
                            _logger.LogInformation($"Changing to parent domain {mappedDomain}");
                            customerDomain = mappedDomain;
                        }
                        string userName = tokenDetails.Upn ?? "unknown_user";

                        string[] folders = ["sentitems", "inbox"];

                        string resource = "/me";

                        // state with an email address says this is a shared mailbox
                        if (!string.IsNullOrEmpty(decodedState?.SharedMailbox))
                        {
                            resource = "/users/" + decodedState?.SharedMailbox;
                        }

                        foreach (string folder in folders)
                        {
                            _logger.LogInformation("Subscribing to " + folder);

                            ClientState clientState = new ClientState()
                            {
                                SharedSecret = sharedSecret,
                                ClientId = tokenDetails.ClientId,
                            };

                            Subscription requestBody = new Subscription
                            {
                                ChangeType = "created",
                                NotificationUrl = _configuration["notificationURL"] ?? "",
                                LifecycleNotificationUrl =
                                    _configuration["lifecycleNotificationUrl"] ?? "",
                                Resource = $"{resource}/mailfolders('{folder}')/messages",
                                ExpirationDateTime = DateTime.UtcNow.Add(TimeSpan.FromDays(6)),
                                ClientState = JsonConvert.SerializeObject(clientState),
                            };

                            Subscription? subscription = await graphClient.Subscriptions.PostAsync(
                                requestBody
                            );
                            if (subscription == null)
                            {
                                _logger.LogInformation("Failed to set up subscription");
                                return await ApiResponseUtility.CreateErrorResponse<string>(
                                    cancellationToken,
                                    req,
                                    HttpStatusCode.BadRequest,
                                    "Failed to set up subscription",
                                    _logger
                                );
                            }
                            string? refreshToken = tr?.refresh_token;

                            string? subscription_id = subscription.Id;

                            if (subscription_id == null)
                            {
                                _logger.LogInformation("No subscription ID returned");
                                return await ApiResponseUtility.CreateErrorResponse<string>(
                                    cancellationToken,
                                    req,
                                    HttpStatusCode.BadRequest,
                                    "No subscription ID returned",
                                    _logger
                                );
                            }

                            _logger.LogInformation(
                                "Have a subscription, adding to tables for user " + userName
                            );

                            MailSubscription ms = new MailSubscription()
                            {
                                PartitionKey = userName,
                                RowKey = subscription_id,
                                Folder = folder,
                            };

                            if (!string.IsNullOrEmpty(decodedState?.SharedMailbox))
                            {
                                ms.SharedMailbox = decodedState?.SharedMailbox;
                            }
                            else
                            {
                                ms.SharedMailbox = ""; // store an empty string so we can filter
                            }

                            await _tableService.AddEntityAsync("Subscriptions", ms, customerDomain);

                            MailSharedSecret msecret = new MailSharedSecret()
                            {
                                PartitionKey = subscription_id,
                                RowKey = userName,
                                SharedSecret = sharedSecret,
                            };

                            await _tableService.AddEntityAsync(
                                "SharedSecrets",
                                msecret,
                                customerDomain
                            );

                            _logger.LogInformation("Added to tables");

                            Result<SecretClient> clientRes = WebhookUtils.GetSecretClient(
                                kvUrl,
                                tokenDetails.ClientId,
                                tokenDetails.ClientSecret
                            );

                            if (clientRes.IsFailed)
                            {
                                _logger.LogError("Failed to get vault access");
                            }
                            else
                            {
                                SecretClient secretClient = clientRes.Value;

                                KeyVaultSecret sessionSecret = new KeyVaultSecret(
                                    WebhookUtils.getRefreshTokenName(subscription_id),
                                    refreshToken
                                );
                                sessionSecret.Properties.ExpiresOn = DateTime.UtcNow.Add(
                                    TimeSpan.FromDays(75)
                                );

                                sessionSecret.Properties.Tags.Add("User", userName);

                                await secretClient.SetSecretAsync(sessionSecret);

                                KeyVaultSecret sessionSecretShared = new KeyVaultSecret(
                                    WebhookUtils.getSharedSecretName(subscription_id),
                                    sharedSecret
                                );

                                sessionSecretShared.Properties.Tags.Add("User", userName);
                                await secretClient.SetSecretAsync(sessionSecretShared);
                            }
                        }
                        HttpResponseData? ccresponse2 = req.CreateResponse(HttpStatusCode.Redirect);

                        ccresponse2.Headers.Add(
                            "Location",
                            "https://static.atvero.com/mail/success.html"
                        );

                        //ccresponse2.Headers.Add("Content-Type", "text/html; charset=utf-8");
                        // await ccresponse2.WriteStringAsync(
                        //     $"<h2>Mail Automation Setup Complete</h2>"
                        //         + "<p>Your automation has been set up successfully.  Please close this window.</p>"
                        // );
                        return ccresponse2;
                    }
                }

                HttpResponseData? ccresponse3 = req.CreateResponse(HttpStatusCode.BadRequest);
                ccresponse3.Headers.Add("Content-Type", "text/plain; charset=utf-8");
                await ccresponse3.WriteStringAsync(BrandingUtils.ProductName + " setup failed");
                return ccresponse3;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed storing using credentials");

                HttpResponseData? ccresponse4 = req.CreateResponse(HttpStatusCode.BadRequest);
                ccresponse4.Headers.Add("Content-Type", "text/plain; charset=utf-8");
                await ccresponse4.WriteStringAsync(BrandingUtils.ProductName + " setup failed");
                return ccresponse4;
            }
        }
    }
}
