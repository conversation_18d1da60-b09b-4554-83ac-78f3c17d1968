using System.Collections.Generic;
using Azure;
using Azure.Data.Tables;
using Microsoft.Extensions.Configuration;

namespace AtveroEmailFilingAzure.UsageStats
{
    public class Program
    {
        public static void Main(string[] args)
        {
            IConfiguration configuration = new ConfigurationBuilder()
                .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
                .Build();

            string connectionString = configuration["AzureTableStorage:ConnectionString"];
            string tableName = configuration["AzureTableStorage:TableName"];

            TableServiceClient tableServiceClient = new TableServiceClient(connectionString);
            TableClient tableClient = tableServiceClient.GetTableClient(tableName);

            AsyncPageable<TableEntity> queryResults = tableClient.QueryAsync<TableEntity>();

            List<TableEntity> entities = new List<TableEntity>();

            Dictionary<string, int> customerStats = new Dictionary<string, int>();

            List<string> customers = customerStats.Keys.ToList();

            for (int i = 0; i < customers.Count; i++)
            {
                string customer = customers[i];
                int count = customerStats[customer];
            }
        }
    }
}
