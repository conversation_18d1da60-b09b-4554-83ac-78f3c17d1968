namespace AtveroEmailFiling.Utils;

public static class SearchSchema
{
    public const string XMLSearchSchema = """
<SearchConfigurationSettings xmlns:i="http://www.w3.org/2001/XMLSchema-instance"
  xmlns="http://schemas.datacontract.org/2004/07/Microsoft.Office.Server.Search.Portability">
  <SearchQueryConfigurationSettings>
    <SearchQueryConfigurationSettings>
      <BestBets xmlns:d4p1="http://www.microsoft.com/sharepoint/search/KnownTypes/2008/08" />
      <DefaultSourceId>00000000-0000-0000-0000-000000000000</DefaultSourceId>
      <DefaultSourceIdSet>false</DefaultSourceIdSet>
      <DeployToParent>false</DeployToParent>
      <DisableInheritanceOnImport>false</DisableInheritanceOnImport>
      <OverriddenQueryRules xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
      <QueryRuleGroups xmlns:d4p1="http://www.microsoft.com/sharepoint/search/KnownTypes/2008/08" />
      <QueryRules xmlns:d4p1="http://www.microsoft.com/sharepoint/search/KnownTypes/2008/08" />
      <ResultTypes xmlns:d4p1="http://schemas.datacontract.org/2004/07/Microsoft.Office.Server.Search.Administration" />
      <Sources xmlns:d4p1="http://schemas.datacontract.org/2004/07/Microsoft.Office.Server.Search.Administration.Query" />
      <UserSegments xmlns:d4p1="http://www.microsoft.com/sharepoint/search/KnownTypes/2008/08" />
    </SearchQueryConfigurationSettings>
  </SearchQueryConfigurationSettings>
  <SearchRankingModelConfigurationSettings>
    <RankingModels xmlns:d3p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
  </SearchRankingModelConfigurationSettings>
  <SearchSchemaConfigurationSettings>
    <Aliases xmlns:d3p1="http://schemas.datacontract.org/2004/07/Microsoft.Office.Server.Search.Administration">
      <d3p1:LastItemName i:nil="true" />
      <d3p1:dictionary xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
    </Aliases>
    <CategoriesAndCrawledProperties xmlns:d3p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
      <d3p1:KeyValueOfguidCrawledPropertyInfoCollectionaSYUqUE_P>
        <d3p1:Key>00130329-0000-0130-c000-000000131346</d3p1:Key>
        <d3p1:Value xmlns:d5p1="http://schemas.datacontract.org/2004/07/Microsoft.Office.Server.Search.Administration">
          <d5p1:LastItemName>ows_EmailSubject</d5p1:LastItemName>
          <d5p1:dictionary>
            <d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
              <d3p1:Key>ows_EmailImportant</d3p1:Key>
              <d3p1:Value>
                <d5p1:Name>ows_EmailImportant</d5p1:Name>
                <d5p1:CategoryName i:nil="true" />
                <d5p1:IsImplicit>false</d5p1:IsImplicit>
                <d5p1:IsMappedToContents>true</d5p1:IsMappedToContents>
                <d5p1:IsNameEnum>false</d5p1:IsNameEnum>
                <d5p1:MappedManagedProperties />
                <d5p1:Propset>00000000-0000-0000-0000-000000000000</d5p1:Propset>
                <d5p1:Samples i:nil="true" />
                <d5p1:SchemaId>1</d5p1:SchemaId>
              </d3p1:Value>
            </d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
            <d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
              <d3p1:Key>ows_EmailTags</d3p1:Key>
              <d3p1:Value>
                <d5p1:Name>ows_EmailTags</d5p1:Name>
                <d5p1:CategoryName i:nil="true" />
                <d5p1:IsImplicit>false</d5p1:IsImplicit>
                <d5p1:IsMappedToContents>true</d5p1:IsMappedToContents>
                <d5p1:IsNameEnum>false</d5p1:IsNameEnum>
                <d5p1:MappedManagedProperties />
                <d5p1:Propset>00000000-0000-0000-0000-000000000000</d5p1:Propset>
                <d5p1:Samples i:nil="true" />
                <d5p1:SchemaId>1</d5p1:SchemaId>
              </d3p1:Value>
            </d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
            <d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
              <d3p1:Key>ows_AttachmentCount</d3p1:Key>
              <d3p1:Value>
                <d5p1:Name>ows_AttachmentCount</d5p1:Name>
                <d5p1:CategoryName i:nil="true" />
                <d5p1:IsImplicit>false</d5p1:IsImplicit>
                <d5p1:IsMappedToContents>true</d5p1:IsMappedToContents>
                <d5p1:IsNameEnum>false</d5p1:IsNameEnum>
                <d5p1:MappedManagedProperties />
                <d5p1:Propset>00000000-0000-0000-0000-000000000000</d5p1:Propset>
                <d5p1:Samples i:nil="true" />
                <d5p1:SchemaId>1</d5p1:SchemaId>
              </d3p1:Value>
            </d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
            <d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
              <d3p1:Key>ows_EmailCC</d3p1:Key>
              <d3p1:Value>
                <d5p1:Name>ows_EmailCC</d5p1:Name>
                <d5p1:CategoryName i:nil="true" />
                <d5p1:IsImplicit>false</d5p1:IsImplicit>
                <d5p1:IsMappedToContents>true</d5p1:IsMappedToContents>
                <d5p1:IsNameEnum>false</d5p1:IsNameEnum>
                <d5p1:MappedManagedProperties />
                <d5p1:Propset>00000000-0000-0000-0000-000000000000</d5p1:Propset>
                <d5p1:Samples i:nil="true" />
                <d5p1:SchemaId>1</d5p1:SchemaId>
              </d3p1:Value>
            </d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
            <d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
              <d3p1:Key>ows_ATVSearchRefiner</d3p1:Key>
              <d3p1:Value>
                <d5p1:Name>ows_ATVSearchRefiner</d5p1:Name>
                <d5p1:CategoryName i:nil="true" />
                <d5p1:IsImplicit>false</d5p1:IsImplicit>
                <d5p1:IsMappedToContents>true</d5p1:IsMappedToContents>
                <d5p1:IsNameEnum>false</d5p1:IsNameEnum>
                <d5p1:MappedManagedProperties />
                <d5p1:Propset>00000000-0000-0000-0000-000000000000</d5p1:Propset>
                <d5p1:Samples i:nil="true" />
                <d5p1:SchemaId>1</d5p1:SchemaId>
              </d3p1:Value>
            </d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
            <d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
              <d3p1:Key>ows_EmailReceivedOn</d3p1:Key>
              <d3p1:Value>
                <d5p1:Name>ows_EmailReceivedOn</d5p1:Name>
                <d5p1:CategoryName i:nil="true" />
                <d5p1:IsImplicit>false</d5p1:IsImplicit>
                <d5p1:IsMappedToContents>true</d5p1:IsMappedToContents>
                <d5p1:IsNameEnum>false</d5p1:IsNameEnum>
                <d5p1:MappedManagedProperties />
                <d5p1:Propset>00000000-0000-0000-0000-000000000000</d5p1:Propset>
                <d5p1:Samples i:nil="true" />
                <d5p1:SchemaId>1</d5p1:SchemaId>
              </d3p1:Value>
            </d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
            <d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
              <d3p1:Key>ows_EmailFrom</d3p1:Key>
              <d3p1:Value>
                <d5p1:Name>ows_EmailFrom</d5p1:Name>
                <d5p1:CategoryName i:nil="true" />
                <d5p1:IsImplicit>false</d5p1:IsImplicit>
                <d5p1:IsMappedToContents>true</d5p1:IsMappedToContents>
                <d5p1:IsNameEnum>false</d5p1:IsNameEnum>
                <d5p1:MappedManagedProperties />
                <d5p1:Propset>00000000-0000-0000-0000-000000000000</d5p1:Propset>
                <d5p1:Samples i:nil="true" />
                <d5p1:SchemaId>1</d5p1:SchemaId>
              </d3p1:Value>
            </d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
            <d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
              <d3p1:Key>ows_EmailTo</d3p1:Key>
              <d3p1:Value>
                <d5p1:Name>ows_EmailTo</d5p1:Name>
                <d5p1:CategoryName i:nil="true" />
                <d5p1:IsImplicit>false</d5p1:IsImplicit>
                <d5p1:IsMappedToContents>true</d5p1:IsMappedToContents>
                <d5p1:IsNameEnum>false</d5p1:IsNameEnum>
                <d5p1:MappedManagedProperties />
                <d5p1:Propset>00000000-0000-0000-0000-000000000000</d5p1:Propset>
                <d5p1:Samples i:nil="true" />
                <d5p1:SchemaId>1</d5p1:SchemaId>
              </d3p1:Value>
            </d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
            <d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
              <d3p1:Key>ows_EmailSubject</d3p1:Key>
              <d3p1:Value>
                <d5p1:Name>ows_EmailSubject</d5p1:Name>
                <d5p1:CategoryName i:nil="true" />
                <d5p1:IsImplicit>false</d5p1:IsImplicit>
                <d5p1:IsMappedToContents>true</d5p1:IsMappedToContents>
                <d5p1:IsNameEnum>false</d5p1:IsNameEnum>
                <d5p1:MappedManagedProperties />
                <d5p1:Propset>00000000-0000-0000-0000-000000000000</d5p1:Propset>
                <d5p1:Samples i:nil="true" />
                <d5p1:SchemaId>1</d5p1:SchemaId>
              </d3p1:Value>
            </d3p1:KeyValueOfstringCrawledPropertyInfoy6h3NzC8>
          </d5p1:dictionary>
        </d3p1:Value>
      </d3p1:KeyValueOfguidCrawledPropertyInfoCollectionaSYUqUE_P>
    </CategoriesAndCrawledProperties>
    <CrawledProperties xmlns:d3p1="http://schemas.datacontract.org/2004/07/Microsoft.Office.Server.Search.Administration">
      <d3p1:LastItemName i:nil="true" />
      <d3p1:dictionary xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays" />
    </CrawledProperties>
    <ManagedProperties xmlns:d3p1="http://schemas.datacontract.org/2004/07/Microsoft.Office.Server.Search.Administration">
      <d3p1:LastItemName>ATVSearchable</d3p1:LastItemName>
      <d3p1:dictionary xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
        <d4p1:KeyValueOfstringManagedPropertyInfoy6h3NzC8>
          <d4p1:Key>ATVSearchable</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>ATVSearchable</d3p1:Name>
            <d3p1:Aliases />
            <d3p1:CompleteMatching>false</d3p1:CompleteMatching>
            <d3p1:Context>0</d3p1:Context>
            <d3p1:DeleteDisallowed>false</d3p1:DeleteDisallowed>
            <d3p1:Description />
            <d3p1:EnabledForScoping>false</d3p1:EnabledForScoping>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExpandSegments>false</d3p1:ExpandSegments>
            <d3p1:FullTextIndex i:nil="true" />
            <d3p1:HasMultipleValues>false</d3p1:HasMultipleValues>
            <d3p1:Hidden>false</d3p1:Hidden>
            <d3p1:IndexOptions>0</d3p1:IndexOptions>
            <d3p1:IsImplicit>false</d3p1:IsImplicit>
            <d3p1:IsReadOnly>false</d3p1:IsReadOnly>
            <d3p1:LanguageNeutralWordBreaker>false</d3p1:LanguageNeutralWordBreaker>
            <d3p1:ManagedType>Text</d3p1:ManagedType>
            <d3p1:MappedCrawledProperties />
            <d3p1:MappingDisallowed>false</d3p1:MappingDisallowed>
            <d3p1:Pid>1001</d3p1:Pid>
            <d3p1:Queryable>true</d3p1:Queryable>
            <d3p1:Refinable>false</d3p1:Refinable>
            <d3p1:RefinerConfiguration>
              <d3p1:Anchoring>Auto</d3p1:Anchoring>
              <d3p1:CutoffMaxBuckets>1000</d3p1:CutoffMaxBuckets>
              <d3p1:Divisor>1</d3p1:Divisor>
              <d3p1:Intervals>4</d3p1:Intervals>
              <d3p1:Resolution>1</d3p1:Resolution>
              <d3p1:Type>Deep</d3p1:Type>
            </d3p1:RefinerConfiguration>
            <d3p1:RemoveDuplicates>false</d3p1:RemoveDuplicates>
            <d3p1:RespectPriority>false</d3p1:RespectPriority>
            <d3p1:Retrievable>true</d3p1:Retrievable>
            <d3p1:SafeForAnonymous>false</d3p1:SafeForAnonymous>
            <d3p1:Searchable>true</d3p1:Searchable>
            <d3p1:Sortable>false</d3p1:Sortable>
            <d3p1:SortableType>Enabled</d3p1:SortableType>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
            <d3p1:UpdateGroup i:nil="true" />
          </d4p1:Value>
        </d4p1:KeyValueOfstringManagedPropertyInfoy6h3NzC8>
      </d3p1:dictionary>
      <d3p1:TotalCount>0</d3p1:TotalCount>
    </ManagedProperties>
    <Mappings xmlns:d3p1="http://schemas.datacontract.org/2004/07/Microsoft.Office.Server.Search.Administration">
      <d3p1:LastItemName>00130329-0000-0130-c000-000000131346:ows_EmailSubject-&gt;1000000031</d3p1:LastItemName>
      <d3p1:dictionary xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_EmailImportant-&gt;1000000052</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_EmailImportant-&gt;1000000052</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_EmailImportant</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000052</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_EmailImportant-&gt;1001</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_EmailImportant-&gt;1001</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_EmailImportant</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1001</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_EmailTags-&gt;1000000051</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_EmailTags-&gt;1000000051</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_EmailTags</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000051</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_AttachmentCount-&gt;1000000709</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_AttachmentCount-&gt;1000000709</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_AttachmentCount</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000709</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_EmailCC-&gt;1000000033</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_EmailCC-&gt;1000000033</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_EmailCC</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000033</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_ATVSearchRefiner-&gt;1000000050</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_ATVSearchRefiner-&gt;1000000050</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_ATVSearchRefiner</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000050</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_ATVSearchRefiner-&gt;1000000010</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_ATVSearchRefiner-&gt;1000000010</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_ATVSearchRefiner</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000010</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_ATVSearchRefiner-&gt;1000000030</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_ATVSearchRefiner-&gt;1000000030</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_ATVSearchRefiner</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000030</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_EmailReceivedOn-&gt;1000000600</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_EmailReceivedOn-&gt;1000000600</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_EmailReceivedOn</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000600</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_EmailReceivedOn-&gt;1000000034</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_EmailReceivedOn-&gt;1000000034</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_EmailReceivedOn</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000034</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_EmailFrom-&gt;1000000035</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_EmailFrom-&gt;1000000035</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_EmailFrom</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000035</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_EmailTo-&gt;1000000032</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_EmailTo-&gt;1000000032</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_EmailTo</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000032</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
        <d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
          <d4p1:Key>00130329-0000-0130-c000-000000131346:ows_EmailSubject-&gt;1000000031</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>00130329-0000-0130-c000-000000131346:ows_EmailSubject-&gt;1000000031</d3p1:Name>
            <d3p1:CrawledPropertyName>ows_EmailSubject</d3p1:CrawledPropertyName>
            <d3p1:CrawledPropset>00130329-0000-0130-c000-000000131346</d3p1:CrawledPropset>
            <d3p1:ManagedPid>1000000031</d3p1:ManagedPid>
            <d3p1:MappingOrder>10</d3p1:MappingOrder>
            <d3p1:SchemaId>0</d3p1:SchemaId>
          </d4p1:Value>
        </d4p1:KeyValueOfstringMappingInfoy6h3NzC8>
      </d3p1:dictionary>
    </Mappings>
    <Overrides xmlns:d3p1="http://schemas.datacontract.org/2004/07/Microsoft.Office.Server.Search.Administration">
      <d3p1:LastItemName>1000000031</d3p1:LastItemName>
      <d3p1:dictionary xmlns:d4p1="http://schemas.microsoft.com/2003/10/Serialization/Arrays">
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000052</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000052</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000052</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000051</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000051</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000051</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000709</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000709</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000709</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000033</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000033</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000033</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000030</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000030</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000030</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000010</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000010</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000010</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000050</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000050</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000050</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000034</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000034</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000034</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000600</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000600</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000600</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000035</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000035</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000035</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000032</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000032</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000032</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
        <d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
          <d4p1:Key>1000000031</d4p1:Key>
          <d4p1:Value>
            <d3p1:Name>1000000031</d3p1:Name>
            <d3p1:AliasesOverridden>false</d3p1:AliasesOverridden>
            <d3p1:EntityExtractorBitMap>0</d3p1:EntityExtractorBitMap>
            <d3p1:ExtraProperties i:nil="true" />
            <d3p1:ManagedPid>1000000031</d3p1:ManagedPid>
            <d3p1:MappingsOverridden>true</d3p1:MappingsOverridden>
            <d3p1:SchemaId>0</d3p1:SchemaId>
            <d3p1:TokenNormalization>true</d3p1:TokenNormalization>
          </d4p1:Value>
        </d4p1:KeyValueOfstringOverrideInfoy6h3NzC8>
      </d3p1:dictionary>
    </Overrides>
  </SearchSchemaConfigurationSettings>
  <SearchSubscriptionSettingsConfigurationSettings i:nil="true" />
  <SearchTaxonomyConfigurationSettings i:nil="true" />
</SearchConfigurationSettings>
""";
}
