﻿using FluentResults;

namespace AtveroEmailFiling.Utils
{
    public static class SharepointUtils
    {
        public static string GetIdFromPath(string input)
        {
            if (string.IsNullOrEmpty(input))
            {
                throw new ArgumentException("Input string cannot be null or empty.", nameof(input));
            }

            string[] parts = input.Split(',');
            if (parts.Length < 3)
            {
                throw new ArgumentException(
                    "Input string does not contain enough parts.",
                    nameof(input)
                );
            }
            return parts[1];
        }

        public static Result<string> GetSiteNameFromUrl(string v)
        {
            if (string.IsNullOrEmpty(v))
            {
                return Result.Fail("Input string cannot be empty.");
            }
            Uri? uri;
            if (!Uri.TryCreate(v, UriKind.Absolute, out uri))
            {
                return Result.Fail("Invalid URL format." + v);
            }
            if (null != uri)
            {
                string[] segments = uri.Segments;
                if (segments.Length == 0)
                {
                    // No path segments, a root site.  Return ""
                    return Result.Ok("");
                }

                return Result.Ok(segments[segments.Length - 1].Trim('/'));
            }
            else
            {
                return Result.Fail("Unable to parse as url " + v);
            }
        }

        public static string GetTenantFromUrl(string sharepointTenant)
        {
            if (!Uri.TryCreate(sharepointTenant, UriKind.Absolute, out Uri? uri))
            {
                throw new ArgumentException("Invalid URL format.", nameof(sharepointTenant));
            }
            if (null == uri)
            {
                throw new ArgumentException(
                    "Unable to parse as uri " + sharepointTenant,
                    nameof(sharepointTenant)
                );
            }
            else
            {
                string host = uri.Host;
                string[] hostParts = host.Split('.');
                if (0 == hostParts.Length)
                {
                    throw new ArgumentException(
                        "URL does not contain a server name.",
                        nameof(sharepointTenant)
                    );
                }

                return hostParts[0];
            }
        }

        public static string GetFullTenantFromUrl(string sharepointTenant)
        {
            if (!Uri.TryCreate(sharepointTenant, UriKind.Absolute, out Uri? uri))
            {
                throw new ArgumentException("Invalid URL format.", nameof(sharepointTenant));
            }
            if (null == uri)
            {
                throw new ArgumentException(
                    "Unable to parse as uri " + sharepointTenant,
                    nameof(sharepointTenant)
                );
            }
            else
            {
                return uri.GetLeftPart(UriPartial.Authority);
            }
        }
    }
}
