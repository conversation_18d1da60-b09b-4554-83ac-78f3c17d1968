using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using FluentResults;
using Microsoft.Extensions.Logging;

namespace AtveroEmailFiling.Utils
{
    public static class StatsUtils
    {
        public static Task LogMessageActiveUserClick(
            IAzureTableService _tableService,
            string customerdomain,
            string email,
            ILogger logger
        )
        {
            string tableName = "AAActiveClickUserStats";
            return LogMessageFiledAction(
                _tableService,
                tableName,
                customerdomain,
                email,
                1,
                logger
            );
        }

        public static Task LogMessageFiledByProject(
            IAzureTableService _tableService,
            string customerdomain,
            string project,
            int count,
            ILogger logger
        )
        {
            string tableName = "AAProjectCustomerStats";
            string rowKey = Uri.EscapeDataString(project);

            return LogMessageFiledAction(
                _tableService,
                tableName,
                customerdomain,
                rowKey,
                count,
                logger
            );
        }

        public static Task LogMessageFiledAutomaticallyForUser(
            IAzureTableService _tableService,
            string customerdomain,
            string email,
            int count,
            ILogger logger
        )
        {
            string tableName = "AAAutoFilingStats";

            return LogMessageFiledAction(
                _tableService,
                tableName,
                customerdomain,
                email,
                count,
                logger
            );
        }

        public static Task LogMessageFiledForUser(
            IAzureTableService _tableService,
            string customerdomain,
            string email,
            int count,
            ILogger logger
        )
        {
            string tableName = "AAUserFilingStats";

            return LogMessageFiledAction(
                _tableService,
                tableName,
                customerdomain,
                email,
                count,
                logger
            );
        }

        public static Task LogMessageFiledOnSend(
            IAzureTableService _tableService,
            string customerdomain,
            string email,
            int count,
            ILogger logger
        )
        {
            string tableName = "AAFiledOnSendStats";

            return LogMessageFiledAction(
                _tableService,
                tableName,
                customerdomain,
                email,
                count,
                logger
            );
        }

        public static Task LogMessageFiledForDomain(
            IAzureTableService _tableService,
            string customerdomain,
            int count,
            ILogger logger
        )
        {
            string tableName = "AADomainFilingStats";

            return LogMessageFiledAction(
                _tableService,
                tableName,
                customerdomain,
                EmailUtils.SanitizeCustomerDomain(customerdomain),
                count,
                logger
            );
        }

        private static async Task LogMessageFiledAction(
            IAzureTableService _tableService,
            string tableName,
            string customerdomain,
            string email,
            int count,
            ILogger logger
        )
        {
            string cleanDomain = EmailUtils.SanitizeCustomerDomain(customerdomain).ToLower();
            string rowKey = email.ToLower();

            try
            {
                var userStat = await _tableService.GetEntityAsync<UserStat>(
                    tableName,
                    cleanDomain,
                    rowKey,
                    ""
                );
                if (userStat.IsSuccess)
                {
                    userStat.Value.FiledCount = userStat.Value.FiledCount + count;
                    userStat.Value.MonthlyFiledCount = userStat.Value.MonthlyFiledCount + count;

                    await _tableService.UpdateEntityAsync(tableName, userStat.Value, "");
                }
                else
                {
                    UserStat newStat = new UserStat()
                    {
                        PartitionKey = cleanDomain,
                        RowKey = rowKey,
                        FiledCount = count,
                        MonthlyFiledCount = count,
                    };

                    await _tableService.UpsertEntityAsync(tableName, newStat, "");
                }
            }
            catch (Exception)
            {
                // don't break anything because we're collecting status
                logger.LogInformation("Failed to add file click to stats table");
                return;
            }
        }
    }
}
