using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;

namespace AtveroEmailFiling.Utils
{
    public static class DomainUtils
    {
        public static async Task<string?> MapDomain(
            string customerDomain,
            IAzureTableService _tableService
        )
        {
            // look up if this is a domain we want to merge together

            string hostFilter = $"PartitionKey eq '{customerDomain}'";

            List<DomainMap> domainMaps = await _tableService.QueryEntitiesAsync<DomainMap>(
                "DomainMap",
                hostFilter,
                "ControlPlane"
            );

            if (domainMaps != null && domainMaps.Count > 0)
            {
                DomainMap parentDomain = domainMaps.First();
                // map to a different domain

                if (parentDomain != null && parentDomain.RowKey != null)
                {
                    return parentDomain.RowKey;
                }
            }

            return null;
        }
    }
}
