﻿using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFiling.Services.GraphApiService.ApiModels;
using Microsoft.Graph;

namespace AtveroEmailFiling.Services.GraphApiService.Sites
{
    /// <summary>
    /// Defines the interface for Graph service operations.
    /// </summary>
    public interface IGraphSiteService
    {
        /// <summary>
        /// Gets a list of associated sites based on the hub site ID and an optional search term.
        /// </summary>
        /// <param name="token">The authentication token.</param>
        /// <param name="hubSiteId">The hub site ID.</param>
        /// <returns>A task that represents the asynchronous operation. The task result contains a list of projects associated with the hub site.</returns>
        Task<List<Project>> GetAssociatedSitesAsync(
            string hubSiteId,
            string? search,
            GraphServiceClient graphClient
        );
    }
}
