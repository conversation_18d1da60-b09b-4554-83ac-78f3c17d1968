﻿using FluentResults;
using Microsoft.Graph;

namespace AtveroEmailFiling.Services.GraphApiService.Drives
{
    public class GraphDriveService : IGraphDriveService
    {
        private readonly IGraphApiClient _graphApiClient;

        public GraphDriveService(IGraphApiClient graphApiClient)
        {
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
        }

        public async Task<Result<string>> GetDriveIdFromProjectCodeAsync(
            string projectCode,
            bool confidential,
            GraphServiceClient graphClient
        )
        {
            if (string.IsNullOrEmpty(projectCode))
            {
                return Result.Fail("Project code cannot be null or empty." + nameof(projectCode));
            }

            try
            {
                return await _graphApiClient.GetDriveIdFromProjectCodeAsync(
                    projectCode,
                    confidential,
                    graphClient
                );
            }
            catch (ArgumentException ex)
            {
                return Result.Fail(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return Result.Fail(ex.Message);
            }
            catch (Exception)
            {
                return Result.Fail("Error getting drive ID from project code.");
            }
        }

        public async Task<Result<string>> GetDriveIdFromSitePathAsync(
            string sitePath,
            bool confidential,
            GraphServiceClient graphClient
        )
        {
            if (string.IsNullOrEmpty(sitePath))
            {
                return Result.Fail("Site path code cannot be null or empty." + nameof(sitePath));
            }

            try
            {
                return await _graphApiClient.GetDriveIdFromSitePathAsync(
                    sitePath,
                    confidential,
                    graphClient
                );
            }
            catch (ArgumentException ex)
            {
                return Result.Fail(ex.Message);
            }
            catch (InvalidOperationException ex)
            {
                return Result.Fail(ex.Message);
            }
            catch (Exception)
            {
                return Result.Fail("Error getting drive ID from site ID.");
            }
        }
    }
}
