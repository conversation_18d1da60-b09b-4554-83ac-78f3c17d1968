﻿using FluentResults;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Microsoft.Graph.Search.Query;

namespace AtveroEmailFiling.Services.GraphApiService
{
    public interface IGraphApiClient
    {
        Task<Result<Message>> GetEmailByInternetIdAsync(
            string internetMessageID,
            GraphServiceClient graphClient,
            string activeUser
        );

        Task<Stream?> GetMessageContentAsync(
            string messageId,
            GraphServiceClient graphClient,
            string activeUser
        );
        Task<DriveItem?> UploadFileAsync(
            string driveId,
            string path,
            Stream content,
            GraphServiceClient graphClient
        );

        Task DeleteFileAsync(string driveId, string path, GraphServiceClient graphClient);
        Task<ListItem?> GetListItemAsync(
            string driveId,
            string itemId,
            GraphServiceClient graphClient
        );
        Task<bool> PatchListItemFieldsAsync(
            string siteId,
            string listId,
            string itemId,
            FieldValueSet fields,
            GraphServiceClient graphClient
        );
        Task<Result<Message>> GetEmailByIdAsync(
            string emailId,
            GraphServiceClient graphClient,
            string activeUser
        );

        Task<Result<List<Message>>> GetEmailsInConversationAsync(
            string conversationId,
            GraphServiceClient graphClient,
            string activeUser
        );
        Task<Site?> GetSiteAsync(string sitePathUrl, GraphServiceClient graphClient);
        Task<DriveCollectionResponse?> GetSiteDrivesAsync(
            string siteId,
            GraphServiceClient graphClient
        );

        Task<string?> GetSpTenantNameAsync(GraphServiceClient graphClient);
        Task<ApiModels.SearchResponse?> SearchSitesAsync(
            QueryPostRequestBody requestBody,
            GraphServiceClient graphClient
        );
        Task<Result<string>> GetDriveIdFromProjectCodeAsync(
            string projectCode,
            bool confidential,
            GraphServiceClient graphClient
        );

        Task<Result<string>> GetDriveIdFromSitePathAsync(
            string sitePath,
            bool confidential,
            GraphServiceClient graphClient
        );

        Task<DriveItem?> GetDriveItemByPathAsync(
            string driveId,
            string path,
            GraphServiceClient graphClient
        );
        Task PatchMessageAsync(
            string messageId,
            Message message,
            GraphServiceClient graphClient,
            string activeUser
        );
        Task<string?> GetUserDisplayNameAsync(GraphServiceClient graphClient);

        Task<DriveCollectionResponse?> GetUserDrivesAsync(GraphServiceClient graphClient);
        Task<Drive?> GetOneDriveAsync(GraphServiceClient graphClient);
        Task<DriveItem?> GetSpecialDriveItemAsync(
            string driveId,
            string specialFolder,
            GraphServiceClient graphClient
        );
        Task<DriveItemCollectionResponse?> GetDriveItemChildrenAsync(
            string driveId,
            string itemId,
            GraphServiceClient graphClient
        );
        Task<DriveItemCollectionResponse?> GetFolderChildrenAsync(
            string driveId,
            string folderId,
            GraphServiceClient graphClient
        );
        Task<Stream?> GetFileContentAsync(
            string driveId,
            string fileId,
            GraphServiceClient graphClient
        );
        Task CreateEmptyFileDirectlyAsync(
            string fileName,
            string token,
            string parentId,
            string driveId,
            GraphServiceClient graphClient
        );

        Task<Result<DriveItem>> UploadFileToFolderAsync(
            string fileName,
            Stream fileContentStream,
            string driveId,
            string folderId,
            GraphServiceClient graphClient
        );

        Task<Result<DriveItem>> GetFolderByNameAsync(
            string driveId,
            string parentId,
            string folderName,
            GraphServiceClient graphClient
        );
        Task<DriveItem?> CreateAppFolderAsync(
            string driveId,
            string parentId,
            string appFolderName,
            GraphServiceClient graphClient
        );

        Task<HttpResponseMessage?> GetEmailDelta(string toke);
        public Task<Result<ListItem>> GetById(
            string listname,
            string id,
            string siteUrl,
            GraphServiceClient graphClient
        );

        public Task<Result<List<ListItem>>> Find(
            string listname,
            string filter,
            string siteId,
            GraphServiceClient graphClient
        );

        public Task<Result<List<ListItem>>> Find(
            string listname,
            string siteId,
            GraphServiceClient graphClient
        );

        public Task<Result<ListItem?>> CreateListItem(
            ListItem item,
            string list,
            string siteId,
            GraphServiceClient graphClient
        );
        public Task<Result<ListItem?>> UpdateListItem(
            ListItem item,
            string list,
            string siteId,
            GraphServiceClient graphClient
        );
        public Task<Result> DeleteListItem(
            string itemId,
            string list,
            string siteId,
            GraphServiceClient graphClient
        );
    }
}
