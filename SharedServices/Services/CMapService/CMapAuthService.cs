using System;
using System.Collections.Generic;
using System.Net.Http.Headers;
using System.Net.Http.Json;
using System.Text.Json;
using System.Threading.Tasks;
using AtveroEmailFiling.Models.CmapProjects;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace AtveroEmailFiling.Services.CMapService
{
    public class CMapAuthService : ICMapAuthService
    {
        private readonly string cMapSubdomain;
        private readonly string? cMapAppKey;
        private readonly string username;
        private readonly string password;
        private readonly HttpClient cmapClient;
        public string? ApiKey
        {
            get { return cMapAppKey; }
        }

        public static async Task<Result<CMapAuthService>> FactoryAsync(
            HttpClient client,
            Microsoft.Extensions.Configuration.IConfiguration config,
            string sharepointTenant,
            ILogger logger
        )
        {
            if (sharepointTenant.StartsWith("https://", StringComparison.OrdinalIgnoreCase))
            {
                sharepointTenant = SharepointUtils.GetTenantFromUrl(sharepointTenant);
            }
            string usernameKey = $"{sharepointTenant}-cmap-username";
            string passwordKey = $"{sharepointTenant}-cmap-password";
            string apiKeyKey = $"{sharepointTenant}-cmap-apikey";
            string cmapDomainKey = $"{sharepointTenant}-cmap-domain";

            logger.LogInformation("UsernameKey is " + usernameKey);
            logger.LogInformation("Username is " + config[usernameKey]);

            if (string.IsNullOrEmpty(config[usernameKey]))
            {
                logger.LogInformation($"No details for {usernameKey} in the vault");
                return Result.Fail($"No details for {usernameKey} in the vault");
            }

            // A bit verbose, but it should send a clear message to the
            // consumer that something is missing from the vault
            if (string.IsNullOrEmpty(config[passwordKey]))
            {
                logger.LogInformation($"No details for {passwordKey} in the vault");
                return Result.Fail($"No details for {passwordKey} in the vault");
            }

            if (string.IsNullOrEmpty(config[apiKeyKey]))
            {
                logger.LogInformation($"No details for {apiKeyKey} in the vault");
                return Result.Fail($"No details for {apiKeyKey} in the vault");
            }

            if (string.IsNullOrEmpty(config[cmapDomainKey]))
            {
                logger.LogInformation($"No details for {cmapDomainKey} in the vault");
                return Result.Fail($"No details for {cmapDomainKey} in the vault");
            }

            if (string.IsNullOrEmpty(config[usernameKey]))
            {
                logger.LogInformation($"No details for {usernameKey} in the vault");
                return Result.Fail($"No details for {usernameKey} in the vault");
            }

            // Checked about the nullability of the config,
            // so I can use the bang operator here
            Result<CMapAuthService> cMapContext = await GetCMapContextAsync(
                client,
                config[apiKeyKey]!,
                config[cmapDomainKey]!,
                config[usernameKey]!,
                config[passwordKey]!,
                (message) => logger.LogInformation(message)
            );

            return cMapContext;
        }

        public static async Task<Result<CMapAuthService>> GetCMapContextAsync(
            HttpClient client,
            string cMapAppKey,
            string cMapDomain,
            string username,
            string password,
            Action<string> log
        )
        {
            CMapAuthService cmapAuth = new CMapAuthService(
                client,
                cMapDomain,
                username,
                password,
                cMapAppKey,
                log
            );

            Result initialised = await cmapAuth.InitialiseAsync();
            if (initialised.IsFailed)
            {
                return initialised;
            }
            else
            {
                return Result.Ok(cmapAuth);
            }
        }

        public CMapAuthService(
            HttpClient client,
            string cMapSubdomain,
            string username,
            string password,
            string? cMapAppKey,
            Action<string> log
        )
        {
            this.cMapAppKey = cMapAppKey;
            this.cmapClient = client;
            this.username = username;
            this.password = password;
            this.cMapSubdomain = cMapSubdomain;
        }

        private HttpClient GetCMapAppAuthClient()
        {
            cmapClient.DefaultRequestHeaders.Add("ClientApi-Key", cMapAppKey);
            return cmapClient;
        }

        private static async Task<Result<string>> GetAppAuthenticationCodeAsync(
            HttpClient httpClient,
            Dictionary<string, string> body
        )
        {
            const string uri = "https://api.cmaphq.com/v1/Auth/Requesttoken";
            try
            {
                HttpResponseMessage response = await httpClient.PostAsJsonAsync<
                    Dictionary<string, string>
                >(uri, body);

                if (response.IsSuccessStatusCode)
                {
                    AuthResponse? authResponse =
                        await response.Content.ReadFromJsonAsync<AuthResponse>();
                    if (null != authResponse?.Token)
                    {
                        return Result.Ok(authResponse.Token);
                    }
                    else
                    {
                        return Result.Fail(
                            new FluentResults.Error(
                                "GetAuthenticationCode - Unable to read response"
                            ).CausedBy(await response.Content.ReadAsStringAsync())
                        );
                    }
                }
                else
                {
                    string reason = await response.Content.ReadAsStringAsync();
                    return Result.Fail(
                        new Error("GetAuthenticationCode - Failed to get auth code").CausedBy(
                            reason
                        )
                    );
                }
            }
            catch (HttpRequestException ex) // Non success
            {
                Console.WriteLine("An error occurred.");
                Console.WriteLine(ex.Message);
                return Result.Fail(
                    new FluentResults.Error(
                        "GetAuthenticationCode - Failed to get auth code with http exception"
                    ).CausedBy(ex)
                );
            }
            catch (NotSupportedException e) // When content type is not valid
            {
                Console.WriteLine("The content type is not supported.");
                return Result.Fail(new Error("GetAuthenticationCode - Not Supported").CausedBy(e));
            }
            catch (JsonException e) // Invalid JSON
            {
                Console.WriteLine("Invalid JSON.");
                return Result.Fail(
                    new Error("GetAuthenticationCode - Invalid JSON received").CausedBy(e)
                );
            }
        }

        public async Task<Result<string>> GetCMapAuthKeyAsync()
        {
            // Get username and password from the secure vault
            Result<Dictionary<string, string>> usernameAndPassword = new Dictionary<string, string>
            {
                { "username", username },
                { "password", password },
                { "subdomain", cMapSubdomain },
            };

            Result<HttpClient> appAuthClient = GetCMapAppAuthClient();
            if (appAuthClient.IsFailed)
            {
                return appAuthClient.ToResult();
            }

            Result<string> authCode = await GetAppAuthenticationCodeAsync(
                appAuthClient.Value,
                usernameAndPassword.Value
            );

            return authCode;
        }

        public async Task<Result<HttpClient>> GetClientAsync()
        {
            if (!cmapClient.DefaultRequestHeaders.Contains("ClientApi-Key"))
            {
                // Get Authorisation Key
                Result<string> authorisationKey = await GetCMapAuthKeyAsync();
                if (authorisationKey.IsFailed)
                {
                    return authorisationKey.ToResult<HttpClient>();
                }

                cmapClient.DefaultRequestHeaders.Add("ClientApi-Key", cMapAppKey);
                cmapClient.DefaultRequestHeaders.Add(
                    "Authorization",
                    $"Bearer {authorisationKey.Value}"
                );
            }

            return Result.Ok(cmapClient);
        }

        public async Task<Result> InitialiseAsync()
        {
            Result<HttpClient> client = await GetClientAsync();
            return client.ToResult();
        }
    }
}
