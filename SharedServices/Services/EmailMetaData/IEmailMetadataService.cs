﻿using Microsoft.Graph;

namespace AtveroEmailFiling.Services.EmailMetadata
{
    public interface IEmailMetadataService
    {
        /// <summary>
        /// Sets metadata for the specified email message.
        /// </summary>

        Task SetEmailMetadata(
            string? messageId,
            string? projectCode,
            GraphServiceClient graphClient,
            string? tag,
            bool important,
            bool confidential,
            string categoryName,
            string filedBy,
            string activeUser
        );

        /// <summary>
        /// Sets the category for a specified email message.
        /// </summary>

        Task SetEmailCategories(
            string messageId,
            GraphServiceClient graphClient,
            string categoryName,
            string activeUser
        );

        /// <summary>
        /// Sets extended properties for a specified email message.
        /// </summary>

        Task SetEmailExtendedProperties(
            string messageId,
            string projectCode,
            string filedBy,
            GraphServiceClient graphClient,
            string tag,
            bool important,
            bool confidential,
            string activeUser
        );
    }
}
