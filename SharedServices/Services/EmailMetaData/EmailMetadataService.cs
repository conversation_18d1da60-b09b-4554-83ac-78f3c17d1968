﻿using AtveroEmailFiling.Services.GraphApiService;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Services.EmailMetadata
{
    public class EmailMetadataService : IEmailMetadataService
    {
        private readonly ILogger<EmailMetadataService> _logger;
        private readonly IGraphApiClient _graphApiClient;
        private readonly IConfiguration _configuration;

        public EmailMetadataService(
            ILogger<EmailMetadataService> logger,
            IGraphApiClient graphApiClient,
            IConfiguration configuration
        )
        {
            _configuration =
                configuration ?? throw new ArgumentNullException(nameof(configuration));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
        }

        public async Task SetEmailMetadata(
            string? messageId,
            string? projectCode,
            GraphServiceClient graphClient,
            string? tag,
            bool important,
            bool confidential,
            string categoryName,
            string filedBy,
            string activeUser
        )
        {
            if (string.IsNullOrWhiteSpace(messageId))
            {
                throw new ArgumentException(
                    "NoTokenFoundMessage ID cannot be null or empty.",
                    nameof(messageId)
                );
            }

            if (string.IsNullOrWhiteSpace(projectCode))
            {
                throw new ArgumentException(
                    "Project code cannot be null or empty.",
                    nameof(projectCode)
                );
            }

            await SetEmailExtendedProperties(
                messageId,
                projectCode,
                filedBy,
                graphClient,
                tag,
                important,
                confidential,
                activeUser
            );

            // do this last, as I susppect it might get moved by rules as soon as we set
            // the category

            await SetEmailCategories(messageId, graphClient, categoryName, activeUser);
        }

        public async Task SetEmailCategories(
            string messageId,
            GraphServiceClient graphClient,
            string categoryName,
            string activeUser
        )
        {
            int retry = 0;
            bool patched = false;

            while (!patched && retry < 5)
            {
                try
                {
                    var requestBody = new Message
                    {
                        Categories = new List<string> { categoryName },
                    };

                    await _graphApiClient.PatchMessageAsync(
                        messageId,
                        requestBody,
                        graphClient,
                        activeUser
                    );

                    patched = true;
                }
                catch (Exception ex)
                {
                    _logger.LogInformation(
                        ex,
                        $"Exception occurred while setting category '{categoryName}' for message ID: {messageId}"
                    );
                }

                retry++;
            }

            if (!patched)
            {
                _logger.LogInformation(
                    $"Exception occurred while setting category '{categoryName}' for message ID: {messageId}, tried 5 times"
                );
                _logger.LogError($"Exception occurred while setting category");
            }
        }

        public async Task SetEmailExtendedProperties(
            string messageId,
            string projectCode,
            string filedBy,
            GraphServiceClient graphClient,
            string? tag,
            bool important,
            bool confidential,
            string activeUser
        )
        {
            string[] PluginIds =
                _configuration["PluginIds"]?.Split(',')
                ?? new string[]
                {
                    "d0d9fb7f-cb01-4a68-a1ad-770b15c0344f" /* dev*/
                    ,
                    "462936ca-5551-407f-977e-4f5f26dbe0f4" /* prod */
                    ,
                    "c0de3d55-3f3e-4be5-b8bd-f44d0f207ab9", /* stage */
                };

            foreach (string pluginId in PluginIds)
            {
                Dictionary<string, object> extendedProperties = new Dictionary<string, object>
                {
                    { "Project", projectCode },
                    { "FiledBy", filedBy },
                    {
                        "FiledOn",
                        DateTime.UtcNow.ToString("o") /* Using ISO 8601 format for date-time*/
                    },
                    { "Important", important },
                    { "Confidential", confidential },
                };

                if (tag != null)
                {
                    extendedProperties.Add("Tag", tag);
                }

                var requestBody = new Message
                {
                    SingleValueExtendedProperties = new List<SingleValueLegacyExtendedProperty>
                    {
                        new SingleValueLegacyExtendedProperty
                        {
                            Id =
                                "String {00020329-0000-0000-C000-000000000046} Name cecp-"
                                + pluginId,
                            Value = JsonConvert.SerializeObject(extendedProperties),
                        },
                    },
                };

                // this seems to conflict with changes often

                int retry = 0;
                bool patched = false;

                while (!patched && retry < 5)
                {
                    try
                    {
                        await _graphApiClient.PatchMessageAsync(
                            messageId,
                            requestBody,
                            graphClient,
                            activeUser
                        );

                        patched = true;
                    }
                    catch (Exception ex)
                    {
                        _logger.LogInformation(
                            ex,
                            $"Exception occurred while setting extended properties for message ID: {messageId} setting properties {JsonConvert.SerializeObject(requestBody)}"
                        );
                        _logger.LogInformation(ex.Message);
                    }
                    retry++;
                }

                if (!patched)
                {
                    _logger.LogInformation(
                        $"Exception occurred while setting extended properties for message ID: {messageId} setting properties {JsonConvert.SerializeObject(requestBody)}.  Stopping after 5 retries"
                    );
                    _logger.LogError(
                        $"Exception occurred while setting extended properties for message.  Stopping after 5 retries"
                    );
                }
            }
        }
    }
}
