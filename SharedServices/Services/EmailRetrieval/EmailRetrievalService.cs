﻿using AtveroEmailFiling.Services.EmailRetrieval;
using FluentResults;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;

namespace AtveroEmailFiling.Services.GraphApiService.Emails
{
    public class EmailRetrievalService : IEmailRetrievalService
    {
        private readonly IGraphApiClient _graphApiClient;
        private readonly ILogger _logger;

        public EmailRetrievalService(IGraphApiClient graphApiClient, ILogger logger)
        {
            _graphApiClient =
                graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        public async Task<Result<List<Message>>> GetEmailsInConversationAsync(
            string conversationId,
            GraphServiceClient graphClient,
            string activeUser
        )
        {
            _logger.LogInformation(
                "Entering GetEmailsInConversationAsync with Conversation ID: {ConversationId}",
                conversationId
            );

            if (string.IsNullOrEmpty(conversationId))
            {
                _logger.LogWarning(
                    "Conversation ID is null or empty. Unable to retrieve emails for Conversation ID: {ConversationId}",
                    conversationId
                );
                return Result.Fail(
                    "Conversation ID cannot be null or empty: " + nameof(conversationId)
                );
            }

            _logger.LogInformation(
                "Starting retrieval of emails in conversation with ID: {ConversationId}",
                conversationId
            );

            try
            {
                Result<List<Message>> messagesResult =
                    await _graphApiClient.GetEmailsInConversationAsync(
                        conversationId,
                        graphClient,
                        activeUser
                    );

                if (messagesResult.IsFailed)
                    return messagesResult;

                // Filter out null messages
                List<Message> messages = messagesResult.Value.Where(m => m != null).ToList();

                _logger.LogInformation(
                    "Filtered null messages. Final count: {FinalMessageCount} emails for Conversation ID: {ConversationId}",
                    messages.Count,
                    conversationId
                );

                return Result.Ok(messages);
            }
            catch (Exception ex)
            {
                _logger.LogInformation(
                    ex,
                    "Exception occurred while retrieving emails for Conversation ID: {ConversationId}",
                    conversationId
                );

                _logger.LogError(
                    ex,
                    "Exception occurred while retrieving emails for Conversation "
                );
                return Result.Fail(
                    "Exception occurred while retrieving emails for Conversation ID: "
                        + conversationId
                );
            }
        }
    }
}
