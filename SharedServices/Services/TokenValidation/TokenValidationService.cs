﻿using System.Globalization;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http.Headers;
using System.Security.Claims;
using AtveroEmailFiling.Models;
using FluentResults;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;

namespace AtveroEmailFiling.Services.TokenValidation
{
    public class TokenValidationService : ITokenValidationService
    {
        private TokenValidationParameters? _validationParameters;
        private readonly IConfiguration _config;

        private readonly ILogger _logger;

        public TokenValidationService(IConfiguration config, ILoggerFactory loggerFactory)
        {
            _config = config;
            _logger = loggerFactory.CreateLogger<TokenValidationService>();
        }

        public async Task<string?> ValidateAuthorizationHeaderAsync(
            Microsoft.Azure.Functions.Worker.Http.HttpRequestData request
        )
        {
            var authHeader = GetAuthorizationHeader(request);
            if (authHeader == null)
            {
                _logger.LogInformation("No Authorization header found or token format invalid");
                return null;
            }

            if (authHeader.Parameter == null)
                return null;

            return await ValidateTokenAsync(authHeader.Parameter);
        }

        private AuthenticationHeaderValue? GetAuthorizationHeader(
            Microsoft.Azure.Functions.Worker.Http.HttpRequestData request
        )
        {
            if (request.Headers.TryGetValues("Authorization", out IEnumerable<string>? authValues))
            {
                var authHeader = AuthenticationHeaderValue.Parse(authValues.First());
                if (IsBearerToken(authHeader))
                {
                    return authHeader;
                }
            }
            return null;
        }

        private static bool IsBearerToken(AuthenticationHeaderValue authHeader)
        {
            return authHeader != null
                && string.Equals(authHeader.Scheme, "bearer", StringComparison.OrdinalIgnoreCase)
                && !string.IsNullOrEmpty(authHeader.Parameter);
        }

        private async Task<string?> ValidateTokenAsync(string token)
        {
            var validationParameters = await GetTokenValidationParametersAsync();
            if (validationParameters == null)
            {
                _logger.LogInformation("Validation parameters are null");
                return null;
            }

            var validatedToken = ValidateToken(token);
            if (string.IsNullOrEmpty(validatedToken))
            {
                _logger.LogInformation("Validation failed, refreshing the validation parameters");
                _validationParameters = null;
                await GetTokenValidationParametersAsync();
                validatedToken = ValidateToken(token);
            }

            return validatedToken;
        }

        public async Task<string?> ValidateCommonAuthorizationHeaderAsync(
            Microsoft.Azure.Functions.Worker.Http.HttpRequestData request
        )
        {
            _logger.LogInformation("ValidateCommonAuthorizationHeaderAsync");

            var authHeader = TryGetAuthorizationHeader(request);

            if (authHeader == null)
            {
                _logger.LogInformation("No Authorization header found or invalid format");
                return null;
            }

            _logger.LogInformation("We have a token");
            var validationParameters = await GetCommonTokenValidationParametersAsync();

            if (validationParameters == null)
            {
                _logger.LogInformation("Validation parameters are null");
                return null;
            }

            if (authHeader.Parameter == null)
                return null;

            var validatedToken = ValidateAndRefreshToken(authHeader.Parameter);

            return validatedToken;
        }

        private static AuthenticationHeaderValue? TryGetAuthorizationHeader(
            Microsoft.Azure.Functions.Worker.Http.HttpRequestData request
        )
        {
            if (!request.Headers.TryGetValues("Authorization", out var authValues))
            {
                return null;
            }

            var firstAuthValue = authValues.FirstOrDefault();

            if (firstAuthValue == null)
                return null;

            var authHeader = AuthenticationHeaderValue.Parse(firstAuthValue);

            if (authHeader?.Parameter == null)
                return null;

            if (
                string.Compare(authHeader.Scheme, "bearer", true, CultureInfo.InvariantCulture) == 0
            )
                return authHeader;
            return null;
        }

        private async Task<TokenValidationParameters?> GetCommonTokenValidationParametersAsync()
        {
            if (_validationParameters != null)
            {
                return _validationParameters;
            }

            var tenantId = _config["tenantId"];

            if (string.IsNullOrEmpty(tenantId))
            {
                _logger.LogError(
                    "GetCommonTokenValidationParametersAsync: Required settings missing: 'tenantId' "
                );
                return null;
            }

            var configManager = new ConfigurationManager<OpenIdConnectConfiguration>(
                $"https://login.microsoftonline.com/{tenantId}/.well-known/openid-configuration",
                new OpenIdConnectConfigurationRetriever()
            );

            var config = await configManager.GetConfigurationAsync();

            _validationParameters = new TokenValidationParameters
            {
                IssuerSigningKeys = config.SigningKeys,
                ValidateAudience = false,
                ValidateIssuer = false,
                ValidateLifetime = true,
            };

            return _validationParameters;
        }

        private string? ValidateAndRefreshToken(string token)
        {
            var validatedToken = ValidateToken(token);

            if (!string.IsNullOrEmpty(validatedToken))
            {
                return validatedToken;
            }

            _logger.LogInformation("Validation failed, refreshing the validation parameters");
            _validationParameters = null;
            var refreshedToken = ValidateToken(token);

            return refreshedToken;
        }

        private string? ValidateToken(string token)
        {
            //_logger.LogInformation("Validating " + token);
            var tokenHandler = new JwtSecurityTokenHandler();

            try
            {
                var result = tokenHandler.ValidateToken(
                    token,
                    _validationParameters,
                    out SecurityToken jwtToken
                );

                //_logger.LogInformation("Valid token");
                return token;
            }
            catch (Exception exception)
            {
                LogTokenValidationError(exception);
            }

            return null;
        }

        private void LogTokenValidationError(Exception exception)
        {
            _logger.LogInformation(exception.Message);
            _logger.LogInformation(exception.ToString());
            _logger.LogError(exception, "Error validating bearer token");
        }

        public bool IsTokenExpired(string token)
        {
            var handler = new JwtSecurityTokenHandler();
            var jwtToken = handler.ReadToken(token) as JwtSecurityToken;
            var expClaim = jwtToken?.Claims.FirstOrDefault(claim => claim.Type == "exp")?.Value;

            if (expClaim != null && long.TryParse(expClaim, out long expTime))
            {
                var expiryDate = DateTimeOffset.FromUnixTimeSeconds(expTime);
                return expiryDate < DateTimeOffset.UtcNow;
            }

            return true; // If there's no exp claim, assume the token is invalid
        }

        /// <summary>
        /// Retrieves the expiry date and time of a given JWT token.
        /// </summary>
        /// <param name="token">The JWT token.</param>
        /// <returns>The expiry date and time of the token if it exists; otherwise, null.</returns>
        public DateTime? GetTokenExpiryDateTime(string token)
        {
            var handler = new JwtSecurityTokenHandler();
            var jwtToken = handler.ReadToken(token) as JwtSecurityToken;
            var expClaim = jwtToken?.Claims.FirstOrDefault(claim => claim.Type == "exp")?.Value;

            if (expClaim != null && long.TryParse(expClaim, out long expTime))
            {
                return DateTimeOffset.FromUnixTimeSeconds(expTime).UtcDateTime;
            }

            return null;
        }

        public TokenDetails GetUserDetails(string token)
        {
            var handler = new JwtSecurityTokenHandler();
            var jwtToken = handler.ReadToken(token) as JwtSecurityToken;

            if (jwtToken == null)
            {
                return new TokenDetails();
            }

            var expClaim = jwtToken.Claims.FirstOrDefault(claim => claim.Type == "exp")?.Value;
            var userIdClaim = jwtToken.Claims.FirstOrDefault(claim => claim.Type == "oid")?.Value;
            var userNameClaim = jwtToken
                .Claims.FirstOrDefault(claim => claim.Type == "name")
                ?.Value;
            var givenNameClaim = jwtToken
                .Claims.FirstOrDefault(claim => claim.Type == "given_name")
                ?.Value;
            var familyNameClaim = jwtToken
                .Claims.FirstOrDefault(claim => claim.Type == "family_name")
                ?.Value;
            // Check for UPN, preferred_username, or email claim
            var upnClaim =
                jwtToken.Claims.FirstOrDefault(claim => claim.Type == "upn")?.Value
                ?? jwtToken
                    .Claims.FirstOrDefault(claim => claim.Type == "preferred_username")
                    ?.Value
                ?? jwtToken.Claims.FirstOrDefault(claim => claim.Type == "email")?.Value;

            DateTime? expiry = null;
            if (expClaim != null && long.TryParse(expClaim, out long expTime))
            {
                expiry = DateTimeOffset.FromUnixTimeSeconds(expTime).UtcDateTime;
            }

            string? audience = jwtToken.Claims.FirstOrDefault(claim => claim.Type == "aud")?.Value;
            _logger.LogInformation("Audience: " + audience);

            if (string.IsNullOrEmpty(audience))
            {
                _logger.LogInformation("No audience claim found in the token");
                throw new Exception("No audience claim found in the token");
            }
            string apiClientId = audience;
            if (Uri.TryCreate(audience, UriKind.Absolute, out Uri? uri))
            {
                apiClientId = uri.LocalPath.StartsWith('/')
                    ? uri.LocalPath.TrimStart('/')
                    : uri.LocalPath;
            }
            string? apiClientSecret = null;
            try
            {
                apiClientSecret = _config[$"{apiClientId}_apiClientSecret"];
            }
            catch (KeyNotFoundException)
            {
                _logger.LogError(
                    $"Key '{apiClientId}_apiClientSecret' not found in configuration."
                );
                apiClientSecret = null;
            }

            if (string.IsNullOrEmpty(apiClientSecret))
            {
                _logger.LogError($"API client secret for '{apiClientId}' is null or empty.");
                throw new Exception($"API client secret for '{apiClientId}' is null or empty.");
            }
            return new TokenDetails
            {
                Expiry = expiry,
                UserId = userIdClaim,
                UserName = userNameClaim,
                GivenName = givenNameClaim,
                FamilyName = familyNameClaim,
                Upn = upnClaim,
                Audience = audience,
                ClientId = apiClientId,
                ClientSecret = apiClientSecret,
            };
        }

        private async Task<TokenValidationParameters?> GetTokenValidationParametersAsync()
        {
            if (_validationParameters == null)
            {
                // Get tenant ID and client ID
                var tenantId = _config["tenantId"];
                if (string.IsNullOrEmpty(tenantId))
                {
                    _logger.LogError(
                        "GetTokenValidationParametersAsync: Required settings missing: 'tenantId'."
                    );
                    return null;
                }

                // Load the tenant-specific OpenID config from Azure
                var configManager = new ConfigurationManager<OpenIdConnectConfiguration>(
                    $"https://login.microsoftonline.com/{tenantId}/.well-known/openid-configuration",
                    new OpenIdConnectConfigurationRetriever()
                );

                var config = await configManager.GetConfigurationAsync();

                _validationParameters = new TokenValidationParameters
                {
                    // Use signing keys retrieved from Azure
                    IssuerSigningKeys = config.SigningKeys,
                    ValidateAudience = false,

                    ValidateIssuer = false, // don't check this for multi tenant
                    // Use the issuer retrieved from Azure
                    // ValidIssuer = config.Issuer,
                    ValidateLifetime = true,
                };
            }

            return _validationParameters;
        }
    }
}
