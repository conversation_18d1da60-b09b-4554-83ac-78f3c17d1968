﻿using Azure.Core;
using Azure.Identity;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.SharePoint.Client;

namespace AtveroEmailFiling.Services.MSCsomClientContextService
{
    public class CsomClientContextService : ICsomClientContextService
    {
        private readonly IConfiguration _config;
        private readonly ILogger _logger;

        public CsomClientContextService(IConfiguration config, ILoggerFactory loggerFactory)
        {
            _config = config;
            _logger = loggerFactory.CreateLogger<CsomClientContextService>();
        }

        public async Task<ClientContext?> GetClientContextAsync(
            string spTenant,
            string? sitePath,
            string userAssertion,
            string clientId,
            string clientSecret
        )
        {
            string? tenantId = _config["tenantId"];

            if (
                string.IsNullOrEmpty(tenantId)
                || string.IsNullOrEmpty(clientId)
                || string.IsNullOrEmpty(clientSecret)
            )
            {
                _logger.LogError(
                    "GetClientContextAsync: Required settings missing: 'tenantId', 'apiClientId', and 'apiClientSecret'."
                );
                return null;
            }

            var onBehalfOfCredential = new OnBehalfOfCredential(
                tenantId,
                clientId,
                clientSecret,
                userAssertion
            );

            var tokenRequestContext = new TokenRequestContext(new[] { $"{spTenant}/.default" });

            // Get the token
            var tokenResult = await onBehalfOfCredential.GetTokenAsync(
                tokenRequestContext,
                new CancellationToken()
            );

            ClientContext context = new ClientContext(
                spTenant + (string.IsNullOrEmpty(sitePath) ? "" : sitePath)
            );

            context.ExecutingWebRequest += (_sender, e) =>
            {
                // Insert the access token in the request
                e.WebRequestExecutor.RequestHeaders["Authorization"] =
                    "Bearer " + tokenResult.Token;
            };

            return context;
        }

        public string? Tenant => _config["tenantId"];
    }
}
