using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace AtveroEmailFilingAzure.src.Services.SharepointApi;

public interface ISharepointApiFactory
{
    public Task<SharepointApi?> CreateSharepointApiAsync(
        HttpRequestData req,
        string sharepointTenant,
        string userAssertion,
        string clientId,
        string clientSecret,
        ILogger logger
    );
}
