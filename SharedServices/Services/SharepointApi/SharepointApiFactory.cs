using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http.Headers;
using System.Threading.Tasks;
using Azure.Core;
using Azure.Identity;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace AtveroEmailFilingAzure.src.Services.SharepointApi;

public class SharepointApiFactory : ISharepointApiFactory
{
    private readonly IConfiguration _config;
    private readonly ILogger _logger;

    public SharepointApiFactory(IConfiguration config, ILoggerFactory loggerFactory)
    {
        _config = config;
        _logger = loggerFactory.CreateLogger<SharepointApiFactory>();
    }

    public async Task<SharepointApi?> CreateSharepointApiAsync(
        HttpRequestData req,
        string sharepointTenant,
        string userAssertion,
        string clientId,
        string clientSecret,
        ILogger logger
    )
    {
        string? tenantId = _config["tenantId"];

        if (string.IsNullOrEmpty(sharepointTenant))
        {
            logger.LogError("Sharepoint tenant URL is null or empty.");
            return null;
        }

        if (string.IsNullOrEmpty(userAssertion))
        {
            logger.LogError("User assertion is null or empty.");
            return null;
        }

        if (
            string.IsNullOrEmpty(tenantId)
            || string.IsNullOrEmpty(clientId)
            || string.IsNullOrEmpty(clientSecret)
        )
        {
            logger.LogError(
                "CreateSharepointApiAsync: Required settings missing: 'tenantId', 'apiClientId', and 'apiClientSecret'."
            );
            return null;
        }

        OnBehalfOfCredential onBehalfOfCredential = new OnBehalfOfCredential(
            tenantId,
            clientId,
            clientSecret,
            userAssertion
        );

        TokenRequestContext tokenRequestContext = new TokenRequestContext(
            new[] { $"{sharepointTenant}/.default" }
        );

        // Get the token
        AccessToken tokenResult = await onBehalfOfCredential.GetTokenAsync(
            tokenRequestContext,
            new CancellationToken()
        );

        HttpClient client = new HttpClient();
        client.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue(
            "Bearer",
            tokenResult.Token
        );
        client.DefaultRequestHeaders.Accept.Add(
            new MediaTypeWithQualityHeaderValue("application/json")
        );

        return new SharepointApi(sharepointTenant, client, logger);
    }
}
