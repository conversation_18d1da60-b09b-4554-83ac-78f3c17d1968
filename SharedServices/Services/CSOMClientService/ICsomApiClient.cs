﻿using AtveroEmailFiling.Models.ApiResponses;
using AtveroEmailFilingAzure.src.Models.ApiResponses;
using FluentResults;
using Microsoft.SharePoint.Client;

namespace AtveroEmailFiling.Services.CsomClientService
{
    public interface ICsomApiClient
    {
        Task<Result> ImportSearchSchemaAsync(string schema, ClientContext csomClientContext);
        Task<Result> GrantHubsiteRights(
            string sitePath,
            string users,
            ClientContext csomClientContext
        );

        Task<Result<List<HubsiteUser>>> GetHubsiteRights(
            string sitePath,
            ClientContext csomClientContext
        );

        Task<Result<bool>> CanWriteToList(string listName, ClientContext csomClientContext);

        Task<Result<List<Project>>> GetProjectsAsync(
            string? search,
            string hubsite,
            ClientContext csomClientContext
        );
    }
}
