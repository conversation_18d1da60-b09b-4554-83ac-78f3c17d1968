namespace AtveroEmailFilingAzure.Services;

using AtveroEmailFiling.Models;
using AtveroEmailFiling.Models.ApiRequests;
using AtveroEmailFiling.Models.AzureTables;
using AtveroEmailFiling.Services.AzureServices.TableService;
using AtveroEmailFiling.Services.EmailMetadata;
using AtveroEmailFiling.Services.EmailUploading;
using AtveroEmailFiling.Services.FilingStatusCheck;
using AtveroEmailFiling.Services.GraphApiService;
using AtveroEmailFiling.Services.GraphApiService.Drives;
using AtveroEmailFiling.Services.GraphApiService.Emails;
using AtveroEmailFiling.Utils;
using FluentResults;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Sentry.Azure.Functions.Worker;

public class ProcessFiledByOthers
{
    private readonly ILogger _logger;
    private readonly IAzureTableService _tableService;
    private readonly IGraphDriveService _graphDriveService;
    private readonly IGraphApiClient _graphApiClient;
    private readonly IFilingStatusCheckService _filingStatusCheckService;
    private readonly IEmailUploadingService _emailUploadingService;
    private readonly IEmailMetadataService _emailMetadataService;

    private readonly IGraphEmailService _graphEmailService;

    private readonly ISyncTableService _syncService;

    public ProcessFiledByOthers(
        ILogger logger,
        IAzureTableService tableService,
        IGraphDriveService graphDriveService,
        IGraphApiClient graphApiClient,
        IFilingStatusCheckService filingStatusCheckService,
        IEmailUploadingService emailUploadingService,
        IEmailMetadataService emailMetadataService,
        IGraphEmailService graphEmailService,
        ISyncTableService syncService
    )
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _tableService = tableService;
        _graphDriveService = graphDriveService;
        _graphApiClient = graphApiClient ?? throw new ArgumentNullException(nameof(graphApiClient));
        _filingStatusCheckService = filingStatusCheckService;
        _emailUploadingService = emailUploadingService;
        _emailMetadataService = emailMetadataService;
        _graphEmailService =
            graphEmailService ?? throw new ArgumentNullException(nameof(graphEmailService));
        _syncService = syncService ?? throw new ArgumentNullException(nameof(syncService));
    }

    public async Task<bool> ProcessQueuedMessages(
        string userName,
        string customerDomainRaw,
        GraphServiceClient graphClient,
        DateTime? tokenExpiry,
        string activeUser
    )
    {
        // keep processing for 25 minutes
        // then stop so we release locks nicely

        var timeoutTime = DateTime.UtcNow.Add(TimeSpan.FromMinutes(25));

        // get list of message IDs filed by other people since we last synched

        string customerDomain = customerDomainRaw;

        string? mappedDomain = await DomainUtils.MapDomain(customerDomainRaw, _tableService);
        if (mappedDomain != null)
        {
            // _logger.LogInformation($"Changing to parent domain {mappedDomain}");
            customerDomain = mappedDomain;
        }

        DateTime runTime = DateTime.UtcNow;

        var lastSyncTime = await _syncService.GetLastRetrievalTimeAsync(activeUser, customerDomain);

        var lastRun = DateTime.MaxValue.Ticks - runTime.Add(TimeSpan.FromDays(-7)).Ticks;

        if (lastSyncTime != null)
        {
            // _logger.LogInformation("We have a saved last sync time so using that");
            lastRun = DateTime.MaxValue.Ticks - lastSyncTime.Value.Ticks;
        }
        else
        {
            _logger.LogInformation("No synced time");
        }

        var filter = string.Format("{0:D19}", lastRun);

        //   _logger.LogInformation("Looking for partition keys lt " + filter);

        List<TimeFiledEmail>? newFiledMessages;
        try
        {
            newFiledMessages = await _tableService.QueryEntitiesAsync<TimeFiledEmail>(
                "TimeFiledEmails",
                $"PartitionKey lt '{filter}'",
                customerDomain
            );
        }
        catch (Exception ex)
        {
            _logger.LogInformation("Failed to access filed messages table " + ex.Message);
            return false;
        }

        if (newFiledMessages != null)
        {
            // _logger.LogInformation(
            //     $"We have {newFiledMessages.Count} filed messages since last time"
            // );

            foreach (TimeFiledEmail filedEmail in newFiledMessages)
            {
                if (DateTime.UtcNow >= timeoutTime)
                {
                    // run out of time
                    break;
                }

                if (tokenExpiry <= DateTime.UtcNow)
                {
                    _logger.LogWarning("Token expired before processing. ");
                    return false;
                }

                if (filedEmail.RowKey != null)
                {
                    // _logger.LogInformation($"Checking if {filedEmail.RowKey} is in this mailbox");

                    Result<Microsoft.Graph.Models.Message> msgResult =
                        await _graphApiClient.GetEmailByInternetIdAsync(
                            EmailUtils.CleanMessageId(filedEmail.RowKey),
                            graphClient,
                            activeUser
                        );

                    if (msgResult.IsSuccess)
                    {
                        Microsoft.Graph.Models.Message msg = msgResult.Value;
                        // _logger.LogInformation(
                        //     "Found a filed by others email,may need to update the metadata on it"
                        // );

                        // if (msg.Categories == null)
                        // {
                        //     _logger.LogInformation("No categories set on target");
                        // }
                        // else
                        // {
                        //     foreach (string category in msg.Categories)
                        //     {
                        //         _logger.LogInformation($"Category {category} is applied");
                        //     }
                        // }

                        if (EmailUtils.AlreadyFiledCategories(msg.Categories))
                        {
                            // _logger.LogInformation("Skipping message as already marked as filed");
                            continue;
                        }

                        // we reverse fetch the actual filed message info to get the metadata

                        Result<FiledEmail> filedMetadataRes =
                            await _tableService.GetEntityAsync<FiledEmail>(
                                "FiledEmails",
                                filedEmail.RowKey,
                                filedEmail.PartitionKey,
                                customerDomain
                            );

                        if (filedMetadataRes.IsSuccess)
                        {
                            FiledEmail first = filedMetadataRes.Value;

                            // _logger.LogInformation("Setting metadata as filed by others");
                            await _emailMetadataService.SetEmailMetadata(
                                msg.Id,
                                first.ProjectCode,
                                graphClient,
                                first.Tag,
                                first.Important,
                                first.Confidential,
                                AtveroEmailFilingAzure.Constants.OTHERS_TAG,
                                first.FiledBy ?? "Filed by unknown",
                                activeUser
                            );
                        }
                        else
                        {
                            _logger.LogInformation("Failed to lookup the message metadata");
                        }
                    }
                }
            }
        }
        else
        {
            _logger.LogError("Unable to retrieve newly filed messages");
            return false;
        }

        await _syncService.UpdateLastRetrievalTimeAsync(activeUser, customerDomain, runTime);
        return true;
    }
}
