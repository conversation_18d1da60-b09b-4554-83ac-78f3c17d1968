﻿using AtveroEmailFiling.Models.AzureTables;
using FluentResults;
using Microsoft.Graph;

namespace AtveroEmailFiling.Services.FilingStatusCheck
{
    public interface IFilingStatusCheckService
    {
        Task AddConversationAsync(Conversation conversation, string customerDomain);

        Task UpsertConversationAsync(Conversation conversation, string customerDomain);

        Task UpsertFiledMessageAsync(FiledEmail filedEmail, string customerDomain);

        Task UpsertTimeFiledMessageAsync(TimeFiledEmail filedEmail, string customerDomain);

        Task<Result<Conversation>> GetConversationAsync(
            string conversationId,
            string customerDomain,
            string projectId
        );

        //  Task UpdateConversationAsync(Conversation conversation, string customerDomain);

        Task<bool> IsEmailAlreadySavedAsync(
            string filingPath,
            string driveId,
            GraphServiceClient graphClient
        );

        // Task<IEnumerable<Conversation>> GetConversationsFiledSinceLastAsync(
        //     string customerDomain,
        //     string projectCode,
        //     DateTime lastRetrievedTime
        // );

        Task<Result<bool>> CheckConversationIsFiledAsync(
            string activeUser,
            string conversationId,
            string customerDomain
        );
    }
}
