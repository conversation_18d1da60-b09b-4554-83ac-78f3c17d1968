using Newtonsoft.Json;

namespace AtveroEmailFiling.Models.QueueMessages
{
    public class FileEmailQueueMessage
    {
        public FileEmailQueueMessage() { }

        [JsonProperty("token")]
        public string? Token { get; set; }

        [JsonProperty("userId")]
        public string? UserId { get; set; }

        [JsonProperty("customerDomain")] // Adding customerDomain for multi-tenancy context
        public string? CustomerDomain { get; set; }

        [JsonProperty("errorDetails")]
        public string? ErrorDetails { get; set; } // For poison queue

        [JsonProperty("sharedMailbox")]
        public string? SharedMailbox { get; set; }
    }
}
