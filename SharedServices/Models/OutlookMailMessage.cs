namespace AtveroEmailFiling.Models
{
    public class OutlookMailMessage
    {
        public required string Id { get; set; }
        public string? Subject { get; set; }
        public string? From { get; set; }
        public string? To { get; set; }
        public string? Cc { get; set; }
        public DateTimeOffset? ReceivedOn { get; set; }
        public string? TextSummary { get; set; }
        public string? InternetMessageId { get; set; }
        public string? ConversationId { get; set; }
        public string SearchRefiner { get; set; } = "Email";
        public DateTimeOffset? EmailReceived { get; set; }
        public int AttachmentCount { get; set; }
        public string? BodyContent { get; set; }
    }
}
