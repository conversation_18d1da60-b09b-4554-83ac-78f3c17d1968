﻿namespace AtveroEmailFiling.Services.AzureServices.QueueService
{
    /// <summary>
    /// Interface for Azure Queue Service to interact with Azure Storage Queues.
    /// </summary>
    /// <typeparam name="T">The type of the message to be enqueued.</typeparam>
    public interface IAzureQueueService<in T>
    {
        /// <summary>
        /// Enqueues a message to the Azure Storage Queue.
        /// </summary>
        /// <param name="message">The message to be enqueued.</param>
        /// <returns>A task that represents the asynchronous enqueue operation.</returns>
        Task EnqueueMessageAsync(T message);

        Task EnqueueMessageUnEncodedAsync(T message);
    }
}
