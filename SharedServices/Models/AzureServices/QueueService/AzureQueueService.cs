﻿using System.Text;
using Azure.Storage.Queues;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFiling.Services.AzureServices.QueueService
{
    /// <summary>
    /// Service for interacting with Azure Storage Queues.
    /// </summary>
    /// <typeparam name="T">The type of the message to be enqueued.</typeparam>
    public class AzureQueueService<T> : IAzureQueueService<T>
    {
        private readonly QueueClient _queueClient;
        private readonly ILogger _logger;

        /// <summary>
        /// Initializes a new instance of the <see cref="AzureQueueService{T}"/> class.
        /// </summary>
        /// <param name="queueClient">The Azure Storage Queue client.</param>
        /// <param name="loggerFactory">The logger factory.</param>
        /// <exception cref="ArgumentNullException">
        /// Thrown when <paramref name="queueClient"/> or <paramref name="loggerFactory"/> is null.
        /// </exception>
        public AzureQueueService(QueueClient queueClient, ILoggerFactory loggerFactory)
        {
            _queueClient = queueClient ?? throw new ArgumentNullException(nameof(queueClient));
            _logger =
                loggerFactory?.CreateLogger<AzureQueueService<T>>()
                ?? throw new ArgumentNullException(nameof(loggerFactory));
        }

        /// <summary>
        /// Enqueues a message to the Azure Storage Queue.
        /// </summary>
        /// <param name="message">The message to be enqueued.</param>
        /// <returns>A task that represents the asynchronous enqueue operation.</returns>
        /// <exception cref="Exception">Thrown when an unexpected error occurs while enqueuing the message.</exception>
        public async Task EnqueueMessageAsync(T message)
        {
            try
            {
                _logger.LogInformation("Attempting to enqueue message to Azure Storage Queue.");

                var messageString = JsonConvert.SerializeObject(message);
                var base64Message = Convert.ToBase64String(Encoding.UTF8.GetBytes(messageString));

                _logger.LogDebug($"Serialized message: {messageString}");
                _logger.LogDebug($"Base64 encoded message: {base64Message}");

                var res = await _queueClient.SendMessageAsync(base64Message);

                _logger.LogInformation(
                    "NoTokenFoundMessage successfully enqueued to Azure Storage Queue."
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "An unexpected exception occurred while enqueuing the message."
                );
                throw;
            }
        }

        public async Task EnqueueMessageUnEncodedAsync(T message)
        {
            try
            {
                _logger.LogInformation("Attempting to enqueue message to Azure Storage Queue.");

                var messageString = JsonConvert.SerializeObject(message);

                _logger.LogDebug($"Serialized message: {messageString}");

                var res = await _queueClient.SendMessageAsync(messageString);

                _logger.LogInformation(
                    "NoTokenFoundMessage successfully enqueued to Azure Storage Queue."
                );
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "An unexpected exception occurred while enqueuing the message."
                );
                throw;
            }
        }
    }
}
