﻿using AtveroEmailFiling.Models.AzureTables;
using Azure;
using Microsoft.Extensions.Logging;

namespace AtveroEmailFiling.Services.AzureServices.TableService
{
    /// <summary>
    /// Service responsible for managing synchronization data stored in Azure Table Storage.
    /// </summary>
    public class SyncTableService : ISyncTableService
    {
        private const int NotFoundStatusCode = 404; // Named constant for the 404 status code
        private readonly IAzureTableService _tableService;
        private readonly ILogger<SyncTableService> _logger;

        /// <summary>
        /// Initializes a new instance of the SyncTableService class.
        /// </summary>
        /// <param name="tableService">An instance of the Azure Table Service.</param>
        public SyncTableService(IAzureTableService tableService, ILogger<SyncTableService> logger)
        {
            _tableService = tableService;
            _logger = logger;
        }

        /// <summary>
        /// Retrieves the last retrieval time for a specific user, customer domain, and project from the Sync table.
        /// </summary>
        /// <param name="userId">The ID of the user for whom the last retrieval time is being fetched.</param>
        /// <param name="customerDomain">The customer domain associated with the sync entry.</param>
        /// <param name="projectCode">The code of the project associated with the sync entry.</param>
        /// <returns>A DateTime? representing the last retrieval time, or null if no data exists.</returns>
        public async Task<DateTime?> GetLastRetrievalTimeAsync(string userId, string customerDomain)
        {
            string partitionKey = userId;

            try
            {
                var syncEntryRes = await _tableService.GetEntityAsync<Sync>(
                    "Sync",
                    partitionKey,
                    customerDomain,
                    customerDomain
                );

                if (syncEntryRes.IsSuccess)
                {
                    return syncEntryRes.Value.LastRetrievalTime;
                }
                else
                {
                    return null;
                }
            }
            catch (RequestFailedException ex) when (ex.Status == NotFoundStatusCode)
            {
                _logger.LogWarning("No sync entry found for user {UserId}", userId);
                return null;
            }
        }

        /// <summary>
        /// Adds or updates the last retrieval time for a specific user, customer domain, and project in the Sync table.
        /// If an entry doesn't exist, it will be created. If it exists, it will be updated.
        /// </summary>
        /// <param name="userId">The ID of the user for whom the last retrieval time is being updated.</param>
        /// <param name="customerDomain">The customer domain associated with the sync entry.</param>
        /// <param name="projectCode">The code of the project associated with the sync entry.</param>
        /// <param name="newRetrievalTime">The new retrieval time to be set.</param>
        public async Task UpdateLastRetrievalTimeAsync(
            string userId,
            string customerDomain,
            DateTime newRetrievalTime
        )
        {
            string partitionKey = $"{userId}_{customerDomain}";

            try
            {
                // If the entity doesn't exist, create a new one
                var syncEntry = new Sync()
                {
                    PartitionKey = userId,
                    RowKey = customerDomain,
                    LastRetrievalTime = newRetrievalTime,
                };

                await _tableService.UpsertEntityAsync("Sync", syncEntry, customerDomain);
                _logger.LogInformation("Successfully updated sync entry for user {UserId}", userId);
            }
            catch (Exception ex)
            {
                _logger.LogError(
                    ex,
                    "Failed to add or update sync entry for user {UserId}",
                    userId
                );

                _logger.LogInformation(ex, "Failed to add or update sync entry for user");
                throw;
            }
        }
    }
}
