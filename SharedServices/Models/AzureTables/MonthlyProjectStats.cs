using Azure;
using Azure.Data.Tables;

namespace AtveroEmailFiling.Models.AzureTables
{
    public class MonthlyProjectStats : ITableEntity
    {
        public MonthlyProjectStats() { }

        public required string PartitionKey { get; set; } // YYYY-MM_CustomerDomain format
        public required string RowKey { get; set; } // Same as original UserStat RowKey (project name)

        public required int MonthlyCount { get; set; }
        public ETag ETag { get; set; }
        public DateTimeOffset? Timestamp { get; set; }
    }
}
