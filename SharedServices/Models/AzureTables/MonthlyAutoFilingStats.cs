using Azure;
using Azure.Data.Tables;

namespace AtveroEmailFiling.Models.AzureTables
{
    public class MonthlyAutoFilingStats : ITableEntity
    {
        public MonthlyAutoFilingStats() { }

        public required string PartitionKey { get; set; } // YYYY-MM_CustomerDomain format
        public required string RowKey { get; set; } // Same as original UserStat RowKey

        public required int MonthlyCount { get; set; }
        public ETag ETag { get; set; }
        public DateTimeOffset? Timestamp { get; set; }
    }
}
