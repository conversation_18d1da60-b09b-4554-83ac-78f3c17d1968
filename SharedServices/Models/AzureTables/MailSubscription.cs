using Azure;
using Azure.Data.Tables;
using Microsoft.Graph.Models;

namespace AtveroEmailFiling.Models.AzureTables
{
    public class MailSubscription : ITableEntity
    {
        public MailSubscription() { }

        public required string PartitionKey { get; set; } // User
        public required string RowKey { get; set; } // Subscription ID
        public string? Folder { get; set; } // Folder
        public string? SharedMailbox { get; set; }

        public DateTimeOffset? Timestamp { get; set; }
        public ETag ETag { get; set; }
    }
}
