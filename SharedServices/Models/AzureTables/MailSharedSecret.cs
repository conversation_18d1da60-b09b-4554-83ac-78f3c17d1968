using Azure;
using Azure.Data.Tables;
using Microsoft.Graph.Models;

namespace AtveroEmailFiling.Models.AzureTables
{
    public class MailSharedSecret : ITableEntity
    {
        public MailSharedSecret() { }

        public required string SharedSecret { get; set; }
        public required string PartitionKey { get; set; } // Subscription ID
        public required string RowKey { get; set; } // User
        public DateTimeOffset? Timestamp { get; set; }
        public ETag ETag { get; set; }
    }
}
