using Azure;
using Azure.Data.Tables;

namespace AtveroEmailFiling.Models.AzureTables
{
    public class UserStat : ITableEntity
    {
        public UserStat() { }

        public required string PartitionKey { get; set; } // Customer Domain
        public required string RowKey { get; set; } // Hubsite Name

        public required int FiledCount { get; set; }

        public required int MonthlyFiledCount { get; set; } = 0;

        public int AllTimeCount { get; set; } = 0;

        public ETag ETag { get; set; }
        public DateTimeOffset? Timestamp { get; set; }
    }
}
