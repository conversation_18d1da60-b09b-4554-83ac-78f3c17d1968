using Azure;
using Azure.Data.Tables;

namespace AtveroEmailFiling.Models.AzureTables
{
    public class MonthlyDomainStats : ITableEntity
    {
        public MonthlyDomainStats() { }

        public required string PartitionKey { get; set; } // YYYY-MM format
        public required string RowKey { get; set; } // Customer Domain (same as original PartitionKey)

        public required int MonthlyCount { get; set; }
        public ETag ETag { get; set; }
        public DateTimeOffset? Timestamp { get; set; }
    }
}
