using Azure;
using Azure.Data.Tables;

namespace AtveroEmailFiling.Models.AzureTables
{
    public class Sync : ITableEntity
    {
        public Sync() { }

        public required string PartitionKey { get; set; } // UserID_CustomerDomain
        public required string RowKey { get; set; } // ProjectCode
        public DateTime LastRetrievalTime { get; set; }

        public ETag ETag { get; set; }
        public DateTimeOffset? Timestamp { get; set; }
    }
}
