using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;

namespace AtveroEmailFilingAzure.Models.ApiRequests;

public class StoreCredientsClientState
{
    [JsonProperty("sharedMailbox")]
    public string? SharedMailbox { get; set; }

    [JsonProperty("clientId")]
    public string? ClientId { get; set; }

    public static StoreCredientsClientState Parse(
        ILogger logger,
        string clientState,
        IConfiguration configuration
    )
    {
        try
        {
            StoreCredientsClientState? state =
                JsonConvert.DeserializeObject<StoreCredientsClientState>(clientState);
            if (null == state)
            {
                logger.LogInformation(
                    "Failed to parse StoreCredientsClientState state, using client state as shared mailbox with the default client id"
                );
                new StoreCredientsClientState()
                {
                    SharedMailbox = clientState,
                    ClientId = configuration["DefaultApiClientId"],
                };
            }
            return state;
        }
        catch (Exception)
        {
            logger.LogInformation(
                "Exception parsing client state, using client state as shared mailbox with the default client id"
            );
            return new StoreCredientsClientState()
            {
                SharedMailbox = clientState,
                ClientId = configuration["DefaultApiClientId"],
            };
        }
    }
}
