using Newtonsoft.Json;

namespace AtveroEmailFiling.Models.ApiRequests
{
    public class CreateSiteRequest
    {
        [JsonProperty("hubsite")]
        public string? HubSite { get; set; }

        [JsonProperty("site")]
        public string? SiteName { get; set; }

        [JsonProperty("description")]
        public string? Description { get; set; }

        [JsonProperty("hubsiteid")]
        public string? HubSiteId { get; set; }
    }

    public class AddHubSiteRequest
    {
        [JsonProperty("hubSiteName")]
        public string? HubSiteName { get; set; }

        [JsonProperty("hubsiteUrl")]
        public string? HubsiteUrl { get; set; }
    }
}
