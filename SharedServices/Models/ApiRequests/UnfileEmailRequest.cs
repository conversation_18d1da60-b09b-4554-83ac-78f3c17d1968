using Newtonsoft.Json;

namespace AtveroEmailFiling.Models.ApiRequests
{
    public class UnfileEmailRequest
    {
        [JsonProperty("driveId")]
        public string? DriveId { get; set; }

        [JsonProperty("id")]
        public string? Id { get; set; }

        [JsonProperty("messageId")]
        public string? MessageId { get; set; }

        [JsonProperty("conversationId")]
        public string? ConversationId { get; set; }

        [JsonProperty("driveItemId")]
        public string? DriveItemId { get; set; }

        [JsonProperty("projectCode")]
        public string? ProjectCode { get; set; }
    }
}
