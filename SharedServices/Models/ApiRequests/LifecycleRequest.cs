using Newtonsoft.Json;

namespace AtveroEmailFiling.Models.ApiRequests
{
    public class LifecycleRequestItem
    {
        [JsonProperty("subscriptionId")]
        public string? SubscriptionId { get; set; }

        [JsonProperty("subscriptionExpirationDateTime")]
        public DateTime? SubscriptionExpirationDateTime { get; set; }

        [JsonProperty("lifecycleEvent")]
        public string? lifecycleEvent { get; set; }

        [JsonProperty("tenantId")]
        public string? TenantID { get; set; }

        [JsonProperty("clientState")]
        public string? clientState { get; set; }
    }

    public class LifecycleRequest
    {
        [JsonProperty("value")]
        public List<LifecycleRequestItem>? request { get; set; }
    }
}
