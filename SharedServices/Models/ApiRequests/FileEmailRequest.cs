using Newtonsoft.Json;

namespace AtveroEmailFiling.Models.ApiRequests
{
    public class FileEmailRequest
    {
        [JsonProperty("hubSite")]
        public string? HubSite { get; set; }

        [JsonProperty("sharedMailbox")]
        public string? SharedMailbox { get; set; }

        [JsonProperty("projectCode")]
        public string? ProjectCode { get; set; }

        [JsonProperty("sitePath")]
        public string? SitePath { get; set; }

        [JsonProperty("siteId")]
        public string? SiteId { get; set; }

        [JsonProperty("tag")]
        public string? Tag { get; set; }

        [JsonProperty("emails")]
        public List<Email>? Emails { get; set; }

        [JsonProperty("isConfidential")]
        public bool Confidential { get; set; }

        [JsonProperty("isImportant")]
        public bool Important { get; set; }
    }

    public class Email
    {
        [JsonProperty("itemId")]
        public string? ItemId { get; set; }
    }
}
