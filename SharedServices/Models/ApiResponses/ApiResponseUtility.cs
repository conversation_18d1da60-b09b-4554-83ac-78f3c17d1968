using System.Net;
using System.Text.Json;
using FluentResults;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;

namespace AtveroEmailFiling.Models.ApiResponses
{
    public static class ApiResponseUtility
    {
        public static async Task<HttpResponseData?> CreateErrorResponse<T>(
            CancellationToken cancellationToken,
            HttpRequestData req,
            HttpStatusCode statusCode,
            string message,
            ILogger logger
        )
        {
            logger.LogWarning("{StatusCode}: {NoTokenFoundMessage}", statusCode, message);

            if (cancellationToken.IsCancellationRequested)
                return null;

            HttpResponseData response = req.CreateResponse(statusCode);

            response.StatusCode = statusCode;
            await WriteJsonResponse(response, new ApiResponse<T>(false, message));
            return response;
        }

        // Overloaded method without logger
        public static async Task<HttpResponseData?> CreateErrorResponse<T>(
            CancellationToken cancellationToken,
            HttpRequestData req,
            HttpStatusCode statusCode,
            string message
        )
        {
            if (cancellationToken.IsCancellationRequested)
                return null;
            HttpResponseData response = req.CreateResponse();

            response.StatusCode = statusCode;
            await WriteJsonResponse(response, new ApiResponse<T>(false, message));
            return response;
        }

        public static async Task<HttpResponseData?> CreateSuccessResponse<T>(
            CancellationToken cancellationToken,
            HttpRequestData req,
            T data,
            string message
        )
        {
            if (cancellationToken.IsCancellationRequested)
                return null;
            HttpResponseData response = req.CreateResponse(HttpStatusCode.OK);

            response.StatusCode = HttpStatusCode.OK;
            await WriteJsonResponse(response, new ApiResponse<T>(true, message, data));
            return response;
        }

        public static async Task<HttpResponseData?> CreateTextSuccessResponse(
            CancellationToken cancellationToken,
            HttpRequestData req,
            string message
        )
        {
            if (cancellationToken.IsCancellationRequested)
                return null;
            HttpResponseData response = req.CreateResponse(HttpStatusCode.OK);

            response.StatusCode = HttpStatusCode.OK;
            await WriteTextResponse(response, message);
            return response;
        }

        public static async Task<HttpResponseData?> HandleBadRequest(
            CancellationToken cancellationToken,
            HttpRequestData req,
            Exception ex,
            string message,
            ILogger logger
        )
        {
            if (cancellationToken.IsCancellationRequested)
                return null;
            HttpResponseData response = req.CreateResponse(HttpStatusCode.BadRequest);
            logger.LogError(ex, message);
            response.StatusCode = HttpStatusCode.BadRequest;
            await WriteJsonResponse(response, new ApiResponse<string>(false, message));
            return response;
        }

        public static async Task<HttpResponseData?> HandleBadRequest(
            CancellationToken cancellationToken,
            HttpRequestData req,
            IEnumerable<Exception> exceptions,
            string message,
            ILogger logger
        )
        {
            if (cancellationToken.IsCancellationRequested)
                return null;
            HttpResponseData response = req.CreateResponse(HttpStatusCode.BadRequest);

            foreach (var ex in exceptions)
            {
                logger.LogError(ex, message);
            }

            await WriteJsonResponse(response, new ApiResponse<string>(false, message));
            return response;
        }

        public static async Task<HttpResponseData?> HandleInternalError(
            CancellationToken cancellationToken,
            HttpRequestData req,
            Exception ex,
            string message,
            ILogger logger
        )
        {
            if (cancellationToken.IsCancellationRequested)
                return null;
            HttpResponseData response = req.CreateResponse(HttpStatusCode.InternalServerError);

            logger.LogError(ex, message);
            response.StatusCode = HttpStatusCode.InternalServerError;
            await WriteJsonResponse(response, new ApiResponse<string>(false, message));
            return response;
        }

        public static async Task<HttpResponseData?> HandleInternalError(
            CancellationToken cancellationToken,
            HttpRequestData req,
            IEnumerable<Exception> exceptions,
            string message,
            ILogger logger
        )
        {
            if (cancellationToken.IsCancellationRequested)
                return null;
            HttpResponseData response = req.CreateResponse(HttpStatusCode.InternalServerError);

            foreach (var ex in exceptions)
            {
                logger.LogError(ex, message);
            }

            await WriteJsonResponse(response, new ApiResponse<string>(false, message));
            return response;
        }

        private static async Task WriteJsonResponse<T>(
            HttpResponseData response,
            ApiResponse<T> apiResponse
        )
        {
            response.Headers.Add("Content-Type", "application/json");
            await response.WriteStringAsync(JsonSerializer.Serialize(apiResponse));
        }

        private static async Task WriteTextResponse(HttpResponseData response, string apiResponse)
        {
            response.Headers.Add("Content-Type", "text/plain");
            await response.WriteStringAsync(apiResponse);
        }

        public static IEnumerable<Exception> ExtractExceptionsFromResult(Result result)
        {
            if (null == result)
            {
                return Array.Empty<Exception>();
            }

            return result
                .Errors.Select(e => e.Reasons.OfType<ExceptionalError>().Select(e => e.Exception))
                .SelectMany(e => e);
        }
    }
}
