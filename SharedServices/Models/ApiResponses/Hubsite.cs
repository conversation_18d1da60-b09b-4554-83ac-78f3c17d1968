using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;

namespace AtveroEmailFilingAzure.src.Models.ApiResponses;

public class Hubsite
{
    public required string ID { get; set; }
    public required object LogoUrl { get; set; }
    public required string ParentHubSiteId { get; set; }
    public int PermissionsSyncTag { get; set; }
    public bool RequiresJoinApproval { get; set; }
    public required string SiteDesignId { get; set; }
    public required string SiteId { get; set; }
    public required string SiteUrl { get; set; }
    public required string Title { get; set; }

    public string Name
    {
        get
        {
            if (string.IsNullOrEmpty(SiteUrl))
            {
                return Title;
            }
            else
            {
                return SiteUrl.Split("/").Last();
            }
        }
    }
}

public class HubsiteRightsResponse
{
    public required string SitePath { get; set; }
    public required List<HubsiteUser> UsersWithRights { get; set; }
}
