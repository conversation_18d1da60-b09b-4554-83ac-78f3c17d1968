# GitHub Actions Workflows

This directory contains GitHub Actions workflows for the Atvero Email Filing project.

## Workflows

### 🚀 `release.yaml`
**Main release workflow** that builds, tests, and deploys the application when a release is created.

- **Trigger**: Release created
- **Jobs**: 
  - Build and deploy application to Azure
  - Create Sentry release for error tracking
- **Dependencies**: None

### 📚 `deploy-release-docs.yml`
**API documentation deployment** that generates and publishes interactive API documentation to GitHub Pages when a release is published.

- **Trigger**: Release published
- **Jobs**:
  - Validate OpenAPI specification
  - Generate interactive HTML documentation using Redoc
  - Deploy to GitHub Pages
  - Comment on release with documentation links
- **Dependencies**: GitHub Pages must be enabled

## Documentation Deployment

### Release Documentation Features

When a release is published, the `deploy-release-docs.yml` workflow automatically:

1. **Validates** the API specification for correctness
2. **Generates** interactive HTML documentation using Redoc
3. **Creates** a release-specific landing page with:
   - Release information and metadata
   - Direct links to documentation sections
   - Styled interface with responsive design
4. **Deploys** to GitHub Pages with release context
5. **Comments** on the release with direct documentation links

### Output Structure

The deployed documentation includes:

```
GitHub Pages Root/
├── index.html              # Interactive API documentation (Redoc)
├── release-info.html       # Release-specific landing page
└── ApiDefinition.yaml      # Raw OpenAPI specification
```

### Access URLs

After deployment, documentation is available at:
- **Main Documentation**: `https://{username}.github.io/{repo}/`
- **Release Info**: `https://{username}.github.io/{repo}/release-info.html`
- **API Spec**: `https://{username}.github.io/{repo}/ApiDefinition.yaml`

## Setup Requirements

### GitHub Pages
1. Go to repository Settings → Pages
2. Set Source to "GitHub Actions"
3. Ensure workflows have `pages: write` permission

### Secrets Required
- `SENTRY_AUTH_TOKEN` - For Sentry release creation
- `SENTRY_ORG` - Sentry organization
- `SENTRY_PROJECT` - Sentry project name
- Azure deployment secrets for application deployment

## Workflow Dependencies

```mermaid
graph TD
    A[Release Created] --> B[release.yaml]
    A --> C[deploy-release-docs.yml]
    B --> D[Application Deployed]
    C --> E[Documentation Deployed]
    D --> F[Sentry Release]
    E --> G[Release Comment Added]
```

## Troubleshooting

### Documentation Deployment Issues
- Ensure GitHub Pages is enabled with "GitHub Actions" source
- Check that API specification is valid YAML
- Verify `pages: write` permission in workflow

### Release Workflow Issues  
- Check Azure deployment secrets are configured
- Verify Sentry secrets for release creation
- Ensure .NET 8.0 compatibility

## Development

To test documentation generation locally:

```bash
# Install Redoc CLI
npm install -g @redocly/cli

# Validate API specification
npx @redocly/cli lint API/ApiDefinition.yaml

# Generate HTML documentation
npx @redocly/cli build-docs API/ApiDefinition.yaml --output=docs.html
```
