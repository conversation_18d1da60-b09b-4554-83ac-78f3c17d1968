name: Build and deploy

env:
  DOTNET_VERSION: "8.0.x" # set this to the dotnet version to use

on:
  pull_request:

jobs:
  merge_conflict_check:
    runs-on: ubuntu-latest
    name: Find merge conflicts
    steps:
      # Checkout the source code so there are some files to look at.
      - uses: actions/checkout@v4
            # Run the actual merge conflict finder
      - name: Merge Conflict finder
        uses: olivernybroe/action-conflict-finder@v4.0

  check_format:
    runs-on: ubuntu-latest
    name: Check for formatting problems
    steps:
      # Checkout the source code so there are some files to look at.
      - uses: actions/checkout@v4
      # Run the actual merge conflict finder
      - name: Run CSharpier
        run: |
          dotnet tool restore
          dotnet csharpier --check .

  build:
    runs-on: ubuntu-latest
    needs: [merge_conflict_check]
    steps:

    - name: Install dependencies
      run: sudo apt-get update && sudo apt-get install -y libxrender1 libfontconfig libxext6 libc6 libxml2

    - uses: actions/checkout@v4

    - name: Setup .NET
      uses: actions/setup-dotnet@v4
      with:
        dotnet-version: ${{ env.DOTNET_VERSION }}     

    - name: Set up JDK 17
      uses: actions/setup-java@v3
      with:
        distribution: 'temurin'
        java-version: '17'



    - name: Restore dependencies
      run: |
        dotnet tool restore
        dotnet restore   


    - name: Sonar and Begin Analysis
      run: >
        dotnet sonarscanner begin
        /o:"cmap-vsts" 
        /k:"cmap-vsts_atveromailbackendazure" 
        /d:sonar.host.url="https://sonarcloud.io" 
        /d:sonar.token="${{ secrets.SONARCLOUD_PAC }}"
        /d:sonar.cs.vscoveragexml.reportsPaths=./coverage.xml 
        /d:sonar.branch.name="${{ github.event.pull_request.head.ref }}" 
        /d:sonar.branch.target="${{ github.event.pull_request.base.ref }}"
        /d:sonar.exclusions="**/AtveroEmailFilingAzure.Tests/**,src/**/Properties/**/*"
        /d:sonar.test.exclusions="**/AtveroEmailFilingAzure.Tests/**"
        /d:sonar.coverage.exclusions="**/AtveroEmailFilingAzure.Tests/**,**/I*.cs"
  
    - name: Generate Models
      run: |
        dotnet run --project DataModel generate --output ./CMapPim/GeneratedModels/
    - name: Build
      run: dotnet build --no-restore

    - name: Test
      run: 
        dotnet coverage collect "dotnet test" -f xml -o "./coverage.xml" 

    - name: End Sonar Analysis 
      run:  dotnet sonarscanner end /d:sonar.login="${{ secrets.SONARCLOUD_PAC }}"
      continue-on-error: true