# Atvero Email Filing Azure Functions

A comprehensive Azure Functions application for email filing to SharePoint, built with .NET 8 and Azure Functions v4. This system provides automated email processing, SharePoint integration, and project information management (PIM) capabilities.

## 🚀 Quick Start

### Prerequisites

- [.NET 8.0 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- [Azure Functions Core Tools v4](https://docs.microsoft.com/en-us/azure/azure-functions/functions-run-local)
- [Visual Studio 2022](https://visualstudio.microsoft.com/) or [VS Code](https://code.visualstudio.com/)
- [Node.js 18+](https://nodejs.org/) (for documentation tools)
- [Redis](https://redis.io/) (for local development)
- [ngrok](https://ngrok.com/) (for webhook testing)

### Clone and Build

```bash
# Clone the repository
git clone <repository-url>
cd AtveroMail/Backend/main

# Restore dependencies
dotnet restore

# Build the solution
dotnet build

# Run tests
dotnet test
```

### Local Development Setup

1. **Configure Local Settings**
   ```bash
   # Copy and configure local settings
   cp AtveroEmailFilingAzure/local.settings.json.example AtveroEmailFilingAzure/local.settings.json
   # Edit the file with your Azure credentials and settings
   ```

2. **Start Redis (Required for local development)**
   ```bash
   # Using Docker
   docker run -d -p 6379:6379 redis:alpine
   
   # Or install locally
   # macOS: brew install redis && brew services start redis
   # Windows: Download from https://redis.io/download
   ```

3. **Setup ngrok for Webhooks (Optional)**
   ```bash
   # Install ngrok
   npm install -g ngrok
   
   # Start tunnel
   ngrok http 7071
   
   # Update local.settings.json with your ngrok URL
   ```

4. **Run the Application**
   ```bash
   cd AtveroEmailFilingAzure
   func start
   ```

## 📁 Project Structure

```
├── AtveroEmailFilingAzure/          # Main Azure Functions project
│   ├── src/Functions/               # Function implementations
│   ├── Program.cs                   # Application entry point
│   ├── host.json                    # Functions host configuration
│   └── local.settings.json          # Local development settings
├── AtveroEmailFilingAzure.Tests/    # Unit tests
├── SharedServices/                  # Shared business logic and services
│   ├── Models/                      # Data models and DTOs
│   ├── Services/                    # Business services
│   └── Utils/                       # Utility classes
├── CMapPim/                         # Project Information Management
│   ├── GeneratedModels/             # Auto-generated SharePoint models
│   ├── Model/                       # Base models and interfaces
│   └── Services/                    # PIM-specific services
├── DataModel/                       # Code generation tool
├── UsageStats/                      # Usage statistics console app
├── API/                             # OpenAPI documentation
└── docs/                            # Project documentation
```

## 🔧 Key Technologies

- **Runtime**: .NET 8.0, Azure Functions v4
- **Authentication**: Microsoft Graph, Azure AD
- **Storage**: Azure Tables, Azure Storage Queues
- **SharePoint**: Microsoft Graph API, CSOM
- **Testing**: MSTest, Moq
- **Documentation**: OpenAPI 3.0, ReDoc
- **Code Quality**: SonarQube, CSharpier

## 🛠️ Development Workflow

### Building the Project

```bash
# Clean and rebuild
dotnet clean
dotnet build

# Build specific project
dotnet build AtveroEmailFilingAzure/AtveroEmailFilingAzure.csproj
```

### Running Tests

```bash
# Run all tests
dotnet test

# Run specific test class
dotnet test --filter "ClassName=ExportStatsTest"

# Run with coverage
dotnet test --collect:"XPlat Code Coverage"
```

### Code Generation

The project uses automatic code generation for SharePoint models:

```bash
# Generate models from schemas
cd DataModel
dotnet run -- generate

# Generate specific model
dotnet run -- generate --class Project --verbose

# Use local schemas
dotnet run -- generate --schema ./schemas --output ./models
```

### Code Formatting

```bash
# Format code with CSharpier
dotnet tool restore
dotnet csharpier .

# Check formatting
dotnet csharpier --check .
```

## 🔑 Configuration

### Required Settings

Update `AtveroEmailFilingAzure/local.settings.json`:

```json
{
  "Values": {
    "AzureWebJobsStorage": "your-storage-connection-string",
    "FUNCTIONS_WORKER_RUNTIME": "dotnet-isolated",
    "tenantId": "your-tenant-id",
    "DefaultApiClientId": "your-client-id",
    "StorageAccountName": "your-storage-account",
    "TableStorageUri": "your-table-storage-uri",
    "AZURE_REDIS_CONNECTIONSTRING": "localhost:6379"
  }
}
```

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `AzureWebJobsStorage` | Azure Storage connection string | Yes |
| `tenantId` | Azure AD tenant ID | Yes |
| `DefaultApiClientId` | Azure AD app registration client ID | Yes |
| `AZURE_REDIS_CONNECTIONSTRING` | Redis connection string | Yes |
| `ENABLE_FILED_BY_OTHERS` | Enable filed by others feature | No |
| `ENABLE_FUTURE_FILING` | Enable future filing feature | No |

## 🧪 Testing

### Test Categories

- **Unit Tests**: Fast, isolated tests for business logic
- **Integration Tests**: Tests with external dependencies
- **API Tests**: End-to-end API testing

### Running Specific Tests

```bash
# Run only unit tests
dotnet test --filter "TestCategory=Unit"

# Run integration tests
dotnet test --filter "TestCategory=Integration"

# Run specific test method
dotnet test --filter "Run_ReturnsOk_WhenValidRequest"
```

### Test Coverage

```bash
# Generate coverage report
dotnet test --collect:"XPlat Code Coverage" --results-directory ./TestResults

# View coverage (requires reportgenerator)
dotnet tool install -g dotnet-reportgenerator-globaltool
reportgenerator -reports:"TestResults/**/coverage.cobertura.xml" -targetdir:"coveragereport" -reporttypes:Html
```

## 📚 API Documentation

### Viewing Documentation

```bash
# Install documentation tools
npm install -g redoc-cli @apidevtools/swagger-parser

# Validate API specification
npx swagger-parser validate API/ApiDefinition.yaml

# Generate and serve documentation
npx redoc-cli serve API/ApiDefinition.yaml --watch
```

### Available Endpoints

- **Email Filing**: `/api/FileEmail` - File emails to SharePoint
- **Project Management**: `/api/Projects` - Manage project information
- **Statistics**: `/api/ExportStats` - Export usage statistics
- **PIM Records**: `/api/GetPimRecords` - Retrieve PIM records
- **Webhooks**: `/api/IncomingSubscription` - Handle Graph webhooks

## 🚀 Deployment

### Local Testing

```bash
# Start the function app
cd AtveroEmailFilingAzure
func start

# Test specific function
curl http://localhost:7071/api/GetInfo
```

### Azure Deployment

The project includes GitHub Actions workflows for automated deployment:

- **Development**: Deploys to dev environment on push to `main`
- **Staging**: Deploys to staging after dev deployment
- **Production**: Manual deployment trigger

## 🔍 Troubleshooting

### Common Issues

1. **Build Failures**
   ```bash
   # Clean and restore
   dotnet clean
   dotnet restore
   dotnet build
   ```

2. **Test Failures**
   ```bash
   # Run tests with detailed output
   dotnet test --verbosity detailed
   ```

3. **Function Runtime Issues**
   ```bash
   # Check function logs
   func logs
   
   # Verify local settings
   cat AtveroEmailFilingAzure/local.settings.json
   ```

4. **Redis Connection Issues**
   ```bash
   # Test Redis connection
   redis-cli ping
   
   # Check Redis is running
   docker ps | grep redis
   ```

### Debug Mode

```bash
# Run with debug logging
cd AtveroEmailFilingAzure
func start --verbose
```

## 🤝 Contributing

1. **Fork the repository**
2. **Create a feature branch**: `git checkout -b feature/amazing-feature`
3. **Make changes and add tests**
4. **Run tests**: `dotnet test`
5. **Format code**: `dotnet csharpier .`
6. **Commit changes**: `git commit -m 'Add amazing feature'`
7. **Push to branch**: `git push origin feature/amazing-feature`
8. **Open a Pull Request**

### Code Standards

- Follow C# coding conventions
- Write unit tests for new features
- Update documentation for API changes
- Use meaningful commit messages
- Ensure all tests pass before submitting PR

## 📄 License

This project is proprietary software owned by Atvero. All rights reserved.

## 🆘 Support

For support and questions:

- **Documentation**: Check the `/docs` directory
- **API Reference**: View generated API docs
- **Issues**: Create GitHub issues for bugs
- **Development**: See troubleshooting section above

---

**Happy Coding! 🎉**
