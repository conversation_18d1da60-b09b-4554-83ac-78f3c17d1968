# ExportStats Azure Function

The ExportStats function allows you to export statistics data from various statistics tables as CSV files. The function has been enhanced with improved code architecture, shared value authentication, and better error handling.

## Endpoint

`GET /api/ExportStats`

## Authentication

**⚠️ IMPORTANT: This function now requires API key authentication for security.**

The function supports multiple authentication methods. You must provide a valid API key using one of the following methods:

### Method 1: Query Parameter
```
GET /api/ExportStats?apiKey=your-secret-key&statsType=UserStats&period=weekly&startYear=2024
```

### Method 2: X-API-Key Header
```bash
curl -H "X-API-Key: your-secret-key" \
  "https://your-function-app.azurewebsites.net/api/ExportStats?statsType=UserStats&period=weekly&startYear=2024"
```

### Method 3: Authorization Header
```bash
curl -H "Authorization: ApiKey your-secret-key" \
  "https://your-function-app.azurewebsites.net/api/ExportStats?statsType=UserStats&period=weekly&startYear=2024"
```

### Configuration
The API key is configured via:
- **Azure Key Vault/Configuration**: `ExportStatsApiKey`
- **Environment Variable**: `EXPORT_STATS_API_KEY`

If no API key is configured, the function will log a warning but remain accessible for backward compatibility.

## Query Parameters

### Required Parameters

- `statsType`: The type of statistics to export
  - Valid values: `UserStats`, `DomainStats`, `AutoFilingStats`, `UserClickStats`, `ProjectStats`
- `period`: The time period type
  - Valid values: `weekly`, `monthly`
- `startYear`: The starting year (4-digit year, e.g., 2024)

### Optional Parameters

- `endYear`: The ending year (defaults to `startYear` if not provided)

### For Weekly Exports

- `week`: Export a single week (1-53)
- `startWeek`: Starting week for range export (1-53)
- `endWeek`: Ending week for range export (1-53)

### For Monthly Exports

- `month`: Export a single month (1-12)
- `startMonth`: Starting month for range export (1-12)
- `endMonth`: Ending month for range export (1-12)

## Usage Examples

**Note**: All examples below include API key authentication. Replace `your-secret-key` with your actual API key.

### Export Single Week
```bash
# Using query parameter
GET /api/ExportStats?apiKey=your-secret-key&statsType=UserStats&period=weekly&startYear=2024&week=15

# Using header
curl -H "X-API-Key: your-secret-key" \
  "https://your-function-app.azurewebsites.net/api/ExportStats?statsType=UserStats&period=weekly&startYear=2024&week=15"
```

### Export Week Range
```bash
# Using query parameter
GET /api/ExportStats?apiKey=your-secret-key&statsType=ProjectStats&period=weekly&startYear=2024&startWeek=10&endWeek=20

# Using Authorization header
curl -H "Authorization: ApiKey your-secret-key" \
  "https://your-function-app.azurewebsites.net/api/ExportStats?statsType=ProjectStats&period=weekly&startYear=2024&startWeek=10&endWeek=20"
```

### Export Single Month
```bash
# Using query parameter
GET /api/ExportStats?apiKey=your-secret-key&statsType=DomainStats&period=monthly&startYear=2024&month=6

# Using header
curl -H "X-API-Key: your-secret-key" \
  "https://your-function-app.azurewebsites.net/api/ExportStats?statsType=DomainStats&period=monthly&startYear=2024&month=6"
```

### Export Month Range
```bash
# Using query parameter
GET /api/ExportStats?apiKey=your-secret-key&statsType=AutoFilingStats&period=monthly&startYear=2024&startMonth=1&endMonth=6
```

### Export Multiple Years
```bash
# Using query parameter
GET /api/ExportStats?apiKey=your-secret-key&statsType=UserClickStats&period=monthly&startYear=2023&endYear=2024&startMonth=10&endMonth=3
```

## Response

The function returns a CSV file with appropriate headers for download. The filename follows the pattern:
`{statsType}_{period}_{startYear}-{endYear}_{periodRange}_{timestamp}.csv`

### CSV Structure

#### For UserStats, AutoFilingStats, UserClickStats, ProjectStats
- `Period`: The time period (YYYY-WW for weekly, YYYY-MM for monthly)
- `CustomerDomain`: The customer domain
- `RowKey`: The specific identifier (user email, project name, etc.)
- `WeeklyCount`/`MonthlyCount`: The count for that period

#### For DomainStats
- `Period`: The time period (YYYY-WW for weekly, YYYY-MM for monthly)
- `CustomerDomain`: The customer domain
- `WeeklyCount`/`MonthlyCount`: The count for that period

## Statistics Tables

The function exports data from the following Azure Tables:

### Weekly Tables
- `AAWeeklyUserStats`
- `AAWeeklyDomainStats`
- `AAWeeklyAutoFilingStats`
- `AAWeeklyUserClickStats`
- `AAWeeklyProjectStats`

### Monthly Tables
- `AAMonthlyUserStats`
- `AAMonthlyDomainStats`
- `AAMonthlyAutoFilingStats`
- `AAMonthlyUserClickStats`
- `AAMonthlyProjectStats`

## Error Responses

- `400 Bad Request`: Invalid query parameters
- `401 Unauthorized`: Missing or invalid authorization token
- `500 Internal Server Error`: Server error during export

## Notes

- CSV values containing commas, quotes, or newlines are properly escaped
- Large exports may take some time to process
- The function queries data based on partition key patterns for efficient retrieval
