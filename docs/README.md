# API Documentation System

This directory contains the automated documentation system for the Atvero Email Filing API.

## 📚 Documentation Files

- `API/ApiDefinition.yaml` - OpenAPI 3.0.3 specification for the API
- Generated HTML documentation (created by GitHub Actions)

## 🔄 Automated Workflows

### Build and Publish Documentation (`build-and-publish-docs.yml`)

**Triggers**:
- After successful completion of deployment workflow (`Push main to dev and staging on cmap.tech`)
- Manual trigger via `workflow_dispatch`

**Dependencies**:
- Requires successful deployment to dev and staging environments
- Only runs if deployment workflow completes successfully

**What it does**:
1. Builds and tests the .NET project
2. Validates the API specification
3. Generates interactive HTML documentation using ReDoc
4. Publishes documentation to:
   - GitHub Wiki
   - GitHub Pages
   - Workflow artifacts

**Outputs**:
- Interactive API documentation at GitHub Pages URL
- Wiki pages with complete documentation
- Downloadable documentation archive

### Release Documentation Deployment (`release.yaml`)

**Triggers**:
- When a new release is created

**Dependencies**:
- Runs after successful application deployment
- Requires GitHub Pages to be enabled

**What it does**:
1. Validates the API specification
2. Generates release-specific HTML documentation
3. Creates a release information page
4. Deploys to GitHub Pages with release context
5. Comments on the release with documentation links

**Outputs**:
- Release-specific API documentation
- Interactive documentation with release information
- Direct links in release comments

### Validate Documentation (`validate-docs.yml`)

**Triggers**: Pull requests affecting documentation files

**What it does**:
1. Validates API specification syntax
2. Tests HTML generation
3. Checks for potential breaking changes
4. Generates preview documentation
5. Comments on PR with validation results

## 🛠️ Local Development

### Prerequisites

```bash
# Install Node.js tools
npm install -g @apidevtools/swagger-parser
npm install -g redoc-cli

# Install .NET SDK 8.0
```

### Validate API Specification Locally

```bash
# Validate specification
npx swagger-parser validate API/ApiDefinition.yaml

# Generate HTML documentation
npx redoc-cli build API/ApiDefinition.yaml --output api-documentation.html
```

### Preview Documentation

```bash
# Generate HTML and serve locally
npx redoc-cli serve API/ApiDefinition.yaml --watch
```

## 📝 Updating Documentation

### API Specification

1. Edit `API/ApiDefinition.yaml` in the API directory
2. Validate locally: `npx swagger-parser validate API/ApiDefinition.yaml`
3. Create a pull request
4. Review the validation results in the PR comment
5. Merge to `main` to publish

## 🔗 Documentation Links

After merging to main, documentation is available at:

- **GitHub Pages**: `https://<username>.github.io/<repository>/`
- **Wiki**: `https://github.com/<username>/<repository>/wiki/API-Documentation`
- **Raw API Specification**: `https://github.com/<username>/<repository>/blob/main/API/ApiDefinition.yaml`

## 🚨 Troubleshooting

### Common Issues

1. **API specification validation fails**:
   - Check YAML syntax
   - Ensure all required fields are present
   - Validate schema references

2. **HTML generation fails**:
   - Usually caused by invalid API specification
   - Check for circular references in schemas

3. **Documentation workflow doesn't trigger**:
   - Check if deployment workflow completed successfully
   - Verify workflow dependency configuration
   - Check workflow permissions and triggers

4. **Wiki publishing fails**:
   - Check repository permissions
   - Ensure wiki is enabled
   - Verify GITHUB_TOKEN has write access

### Debug Steps

1. Check workflow logs in GitHub Actions
2. Download preview artifacts from PR validation
3. Run validation commands locally
4. Check file permissions and paths

## 🔧 Configuration

### GitHub Repository Settings

Required settings for full functionality:

1. **Actions permissions**: Allow GitHub Actions to create and approve pull requests
2. **Pages**: Enable GitHub Pages from Actions
3. **Wiki**: Enable repository wiki
4. **Secrets**: No additional secrets required (uses GITHUB_TOKEN)

### Workflow Customization

Key configuration points in the workflows:

- `.NET version`: Update `DOTNET_VERSION` environment variable
- `Documentation tools`: Modify npm package versions in install steps
- `Output paths`: Adjust paths in the documentation generation steps
- `Notification settings`: Customize PR comments and commit comments

## 📊 Monitoring

The workflows provide:

- ✅ Build status badges
- 📝 PR validation comments
- 🔗 Automatic links to published documentation
- 📦 Downloadable documentation artifacts
- 📈 Workflow run history and logs

## 🤝 Contributing

When contributing to the documentation:

1. Always validate changes locally first
2. Include examples for new endpoints
3. Update schema definitions for new models
4. Test the generated HTML documentation
5. Check that diagrams render correctly

The automated validation will catch most issues, but local testing ensures a smooth review process.
